@echo off
echo Adding NO-UNITY runtime staff display and wiring it in...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "DISPLAY_DIR=%PROJ_DIR%\Displays"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

if not exist "%DISPLAY_DIR%" mkdir "%DISPLAY_DIR%"

REM 1) Write display that builds a simple staff at runtime (no AssetBundle required)
(
echo using BTD_Mod_Helper.Api.Display;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Unity.Display;
echo using UnityEngine;
echo.
echo namespace LightningMonkey.Displays {
	echo   public class LightningMonkeyDisplay : ModTowerDisplay<LightningMonkey.Towers.LightningMonkeyTower> {
		echo     public override string BaseDisplay =^> GetDisplay(Il2CppAssets.Scripts.Models.TowerSets.TowerType.DartMonkey);
		echo     public override void ModifyDisplayNode(UnityDisplayNode node) {
			echo       var hand = node.transform.Find("RHand") ?? node.transform.Find("RightHand") ?? node.transform.Find("weapon") ?? node.transform;
			echo       var staffRoot = new GameObject("RuntimeStaff");
			echo       staffRoot.transform.SetParent(hand, false);
			echo       staffRoot.transform.localPosition = new Vector3(0.05f, 0.02f, 0f);
			echo       staffRoot.transform.localRotation = Quaternion.Euler(0f, 0f, -20f);
			echo       staffRoot.transform.localScale = Vector3.one * 0.85f;
			echo       // Shaft
			echo       var shaft = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
			echo       var coll1 = shaft.GetComponent<Collider>(); if ^(coll1 != null^) Object.Destroy(coll1);
			echo       shaft.transform.SetParent(staffRoot.transform, false);
			echo       shaft.transform.localScale = new Vector3(0.08f, 0.75f, 0.08f);
			echo       shaft.transform.localPosition = new Vector3(0f, 0.75f, 0f);
			echo       var r1 = shaft.GetComponent<Renderer>();
			echo       if ^(r1 != null^) { var m = new Material(r1.material); m.color = new Color(0.25f, 0.15f, 0.05f, 1f); r1.material = m; }
			echo       // Head piece 1
			echo       var head = GameObject.CreatePrimitive(PrimitiveType.Cube);
			echo       var coll2 = head.GetComponent<Collider>(); if ^(coll2 != null^) Object.Destroy(coll2);
			echo       head.transform.SetParent(staffRoot.transform, false);
			echo       head.transform.localScale = new Vector3(0.22f, 0.22f, 0.22f);
			echo       head.transform.localPosition = new Vector3(0f, 1.55f, 0f);
			echo       head.transform.localRotation = Quaternion.Euler(0f, 0f, 45f);
			echo       var r2 = head.GetComponent<Renderer>();
			echo       if ^(r2 != null^) { var m2 = new Material(r2.material); var c = new Color(0.25f, 0.95f, 1f, 1f); m2.color = c; m2.EnableKeyword("_EMISSION"); m2.SetColor("_EmissionColor", c * 1.5f); r2.material = m2; }
			echo       // Head piece 2
			echo       var head2 = GameObject.CreatePrimitive(PrimitiveType.Cube);
			echo       var coll3 = head2.GetComponent<Collider>(); if ^(coll3 != null^) Object.Destroy(coll3);
			echo       head2.transform.SetParent(staffRoot.transform, false);
			echo       head2.transform.localScale = new Vector3(0.18f, 0.18f, 0.18f);
			echo       head2.transform.localPosition = new Vector3(0.12f, 1.72f, 0f);
			echo       head2.transform.localRotation = Quaternion.Euler(0f, 0f, -45f);
			echo       var r3 = head2.GetComponent<Renderer>();
			echo       if ^(r3 != null^) { var m3 = new Material(r3.material); var c2 = new Color(0.25f, 0.95f, 1f, 1f); m3.color = c2; m3.EnableKeyword("_EMISSION"); m3.SetColor("_EmissionColor", c2 * 1.5f); r3.material = m3; }
			echo     }
		echo   }
	echo }
) > "%DISPLAY_DIR%\LightningMonkeyDisplay.cs"

REM 2) Ensure tower applies the display line once at start of ModifyBaseTowerModel
powershell -Command "^$
$file = '%TOWER_FILE%';^n$src = Get-Content -Raw $file;^nif ($src -notmatch 'ApplyDisplay<LightningMonkey\.Displays\.LightningMonkeyDisplay>') {^n  $src = $src -replace '(ModifyBaseTowerModel\([^{]+\)\s*\{)', '$1`r`n      model.ApplyDisplay<LightningMonkey.Displays.LightningMonkeyDisplay>();';^n  Set-Content -Path $file -Value $src;^n}^n"

REM 3) Build
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

if %ERRORLEVEL% EQU 0 (
  echo ========================================
  echo SUCCESS! No-Unity staff attached to display.
  echo ========================================
  echo - The monkey uses Dart visuals and now holds a generated staff.
  echo - You can tweak position/rotation/scale in Displays\LightningMonkeyDisplay.cs
) else (
  echo Build failed. Fix compile errors above.
)
pause
