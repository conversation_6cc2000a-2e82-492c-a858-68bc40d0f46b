{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"LightningMonkey/1.0.0": {"dependencies": {"Assembly-CSharp": "0.0.0.0", "Btd6ModHelper": "1.0.0.0", "Il2CppInterop.Runtime": "*******", "Il2Cppmscorlib": "*******", "Il2CppNinjaKiwi.Common": "0.0.0.0", "Il2CppSystem": "*******", "MelonLoader": "0.7.0.0", "UnityEngine.CoreModule": "0.0.0.0", "UnityEngine": "0.0.0.0"}, "runtime": {"LightningMonkey.dll": {}}}, "Assembly-CSharp/0.0.0.0": {"runtime": {"Assembly-CSharp.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Btd6ModHelper/1.0.0.0": {"runtime": {"Btd6ModHelper.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Il2CppInterop.Runtime/*******": {"runtime": {"Il2CppInterop.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2Cppmscorlib/*******": {"runtime": {"Il2Cppmscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppNinjaKiwi.Common/0.0.0.0": {"runtime": {"Il2CppNinjaKiwi.Common.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem/*******": {"runtime": {"Il2CppSystem.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "MelonLoader/0.7.0.0": {"runtime": {"MelonLoader.dll": {"assemblyVersion": "0.7.0.0", "fileVersion": "0.7.0.0"}}}, "UnityEngine.CoreModule/0.0.0.0": {"runtime": {"UnityEngine.CoreModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine/0.0.0.0": {"runtime": {"UnityEngine.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.InputSystem/1.14.1.0": {"runtime": {"Unity.InputSystem.dll": {"assemblyVersion": "1.14.1.0", "fileVersion": "0.0.0.0"}}}, "Unity.TextMeshPro/0.0.0.0": {"runtime": {"Unity.TextMeshPro.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UI/1.0.0.0": {"runtime": {"UnityEngine.UI.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AudioModule/0.0.0.0": {"runtime": {"UnityEngine.AudioModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AnimationModule/0.0.0.0": {"runtime": {"UnityEngine.AnimationModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.ParticleSystemModule/0.0.0.0": {"runtime": {"UnityEngine.ParticleSystemModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppNinjaKiwi.GUTS/0.0.0.0": {"runtime": {"Il2CppNinjaKiwi.GUTS.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppNinjaKiwi.LiNK/********": {"runtime": {"Il2CppNinjaKiwi.LiNK.dll": {"assemblyVersion": "********", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Purchasing/0.0.0.0": {"runtime": {"UnityEngine.Purchasing.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Core/*******": {"runtime": {"Il2CppSystem.Core.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Unity.ResourceManager/0.0.0.0": {"runtime": {"Unity.ResourceManager.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.VideoModule/0.0.0.0": {"runtime": {"UnityEngine.VideoModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.InputLegacyModule/0.0.0.0": {"runtime": {"UnityEngine.InputLegacyModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.PhysicsModule/0.0.0.0": {"runtime": {"UnityEngine.PhysicsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UIModule/0.0.0.0": {"runtime": {"UnityEngine.UIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TextRenderingModule/0.0.0.0": {"runtime": {"UnityEngine.TextRenderingModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Addressables/0.0.0.0": {"runtime": {"Unity.Addressables.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppNewtonsoft.Json/********": {"runtime": {"Il2CppNewtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "0.0.0.0"}}}, "Il2CppUniWebView-CSharp/0.0.0.0": {"runtime": {"Il2CppUniWebView-CSharp.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppFacepunch.Steamworks/0.0.0.0": {"runtime": {"Il2CppFacepunch.Steamworks.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.IMGUIModule/0.0.0.0": {"runtime": {"UnityEngine.IMGUIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppNinjaKiwi.LiNK.Client/********": {"runtime": {"Il2CppNinjaKiwi.LiNK.Client.dll": {"assemblyVersion": "********", "fileVersion": "0.0.0.0"}}}, "Il2CppNativeShare.Runtime/0.0.0.0": {"runtime": {"Il2CppNativeShare.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Collections/0.0.0.0": {"runtime": {"Unity.Collections.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Mathematics/0.0.0.0": {"runtime": {"Unity.Mathematics.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Assembly-CSharp-firstpass/0.0.0.0": {"runtime": {"Assembly-CSharp-firstpass.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppTwitch/0.0.0.0": {"runtime": {"Il2CppTwitch.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.2D.SpriteShape.Runtime/0.0.0.0": {"runtime": {"Unity.2D.SpriteShape.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.SpriteShapeModule/0.0.0.0": {"runtime": {"UnityEngine.SpriteShapeModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Purchasing.Stores/0.0.0.0": {"runtime": {"UnityEngine.Purchasing.Stores.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UIElementsModule/0.0.0.0": {"runtime": {"UnityEngine.UIElementsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityWebRequestModule/0.0.0.0": {"runtime": {"UnityEngine.UnityWebRequestModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppInterop.Common/*******": {"runtime": {"Il2CppInterop.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppMono.Security/*******": {"runtime": {"Il2CppMono.Security.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Configuration/*******": {"runtime": {"Il2CppSystem.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Xml/*******": {"runtime": {"Il2CppSystem.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "0Harmony/********": {"runtime": {"0Harmony.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MonoMod.Utils/*********": {"runtime": {"MonoMod.Utils.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "MonoMod.RuntimeDetour/*********": {"runtime": {"MonoMod.RuntimeDetour.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "bHapticsLib/*******": {"runtime": {"bHapticsLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AssetRipper.VersionUtilities/*******": {"runtime": {"AssetRipper.VersionUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Tomlet/*******": {"runtime": {"Tomlet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mono.Cecil/********": {"runtime": {"Mono.Cecil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AssetsTools.NET/*******": {"runtime": {"AssetsTools.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver.DotNet/*******": {"runtime": {"AsmResolver.DotNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver.PE/*******": {"runtime": {"AsmResolver.PE.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver/*******": {"runtime": {"AsmResolver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.Generator/*******": {"runtime": {"Il2CppInterop.Generator.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Diagnostics.Runtime/3.1.10.12801": {"runtime": {"Microsoft.Diagnostics.Runtime.dll": {"assemblyVersion": "3.1.10.12801", "fileVersion": "3.1.10.12801"}}}, "Mono.Cecil.Mdb/********": {"runtime": {"Mono.Cecil.Mdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Pdb/********": {"runtime": {"Mono.Cecil.Pdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Rocks/********": {"runtime": {"Mono.Cecil.Rocks.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AsmResolver.PE.File/*******": {"runtime": {"AsmResolver.PE.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.HarmonySupport/*******": {"runtime": {"Il2CppInterop.HarmonySupport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "UnityEngine.AssetBundleModule/0.0.0.0": {"runtime": {"UnityEngine.AssetBundleModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Microsoft.Extensions.Logging.Abstractions/*******": {"runtime": {"Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "UnityEngine.XRModule/0.0.0.0": {"runtime": {"UnityEngine.XRModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.InputModule/0.0.0.0": {"runtime": {"UnityEngine.InputModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TextCoreFontEngineModule/0.0.0.0": {"runtime": {"UnityEngine.TextCoreFontEngineModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Physics2DModule/0.0.0.0": {"runtime": {"UnityEngine.Physics2DModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Net.Http/*******": {"runtime": {"Il2CppSystem.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Internal/0.0.0.0": {"runtime": {"Unity.Services.Core.Internal.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Profiling.Core/0.0.0.0": {"runtime": {"Unity.Profiling.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Numerics/*******": {"runtime": {"Il2CppSystem.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Runtime.Serialization/*******": {"runtime": {"Il2CppSystem.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Data/*******": {"runtime": {"Il2CppSystem.Data.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Xml.Linq/*******": {"runtime": {"Il2CppSystem.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TextCoreTextEngineModule/0.0.0.0": {"runtime": {"UnityEngine.TextCoreTextEngineModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Burst/0.0.0.0": {"runtime": {"Unity.Burst.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.2D.Common.Runtime/0.0.0.0": {"runtime": {"Unity.2D.Common.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AndroidJNIModule/0.0.0.0": {"runtime": {"UnityEngine.AndroidJNIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Purchasing.SecurityStub/0.0.0.0": {"runtime": {"UnityEngine.Purchasing.SecurityStub.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Purchasing.AppleCore/0.0.0.0": {"runtime": {"UnityEngine.Purchasing.AppleCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Purchasing.SecurityCore/0.0.0.0": {"runtime": {"UnityEngine.Purchasing.SecurityCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppPurchasing.Common/0.0.0.0": {"runtime": {"Il2CppPurchasing.Common.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Purchasing.WinRTCore/0.0.0.0": {"runtime": {"UnityEngine.Purchasing.WinRTCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.PropertiesModule/0.0.0.0": {"runtime": {"UnityEngine.PropertiesModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.HierarchyCoreModule/0.0.0.0": {"runtime": {"UnityEngine.HierarchyCoreModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.InputForUIModule/0.0.0.0": {"runtime": {"UnityEngine.InputForUIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.JSONSerializeModule/0.0.0.0": {"runtime": {"UnityEngine.JSONSerializeModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "WebSocketDotNet/1.0.0.0": {"runtime": {"WebSocketDotNet.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "MonoMod.Backports/1.1.2.0": {"runtime": {"MonoMod.Backports.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}}, "Microsoft.Diagnostics.NETCore.Client/0.2.8.10101": {"runtime": {"Microsoft.Diagnostics.NETCore.Client.dll": {"assemblyVersion": "0.2.8.10101", "fileVersion": "0.2.8.10101"}}}, "UnityEngine.SubsystemsModule/0.0.0.0": {"runtime": {"UnityEngine.SubsystemsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core/0.0.0.0": {"runtime": {"Unity.Services.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "MonoMod.ILHelpers/1.1.0.0": {"runtime": {"MonoMod.ILHelpers.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"LightningMonkey/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Assembly-CSharp/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Btd6ModHelper/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Runtime/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2Cppmscorlib/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNinjaKiwi.Common/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "MelonLoader/0.7.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.CoreModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.InputSystem/1.14.1.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.TextMeshPro/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UI/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AudioModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AnimationModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ParticleSystemModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNinjaKiwi.GUTS/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNinjaKiwi.LiNK/********": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Purchasing/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.ResourceManager/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.VideoModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.InputLegacyModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.PhysicsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TextRenderingModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Addressables/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNewtonsoft.Json/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppUniWebView-CSharp/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppFacepunch.Steamworks/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.IMGUIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNinjaKiwi.LiNK.Client/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNativeShare.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Collections/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Mathematics/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Assembly-CSharp-firstpass/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppTwitch/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.2D.SpriteShape.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.SpriteShapeModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Purchasing.Stores/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UIElementsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityWebRequestModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppMono.Security/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Configuration/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Xml/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "0Harmony/********": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.Utils/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.RuntimeDetour/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "bHapticsLib/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetRipper.VersionUtilities/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Tomlet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil/********": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetsTools.NET/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.DotNet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.PE/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Generator/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Diagnostics.Runtime/3.1.10.12801": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Mdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Pdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Rocks/********": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.PE.File/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.HarmonySupport/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AssetBundleModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.XRModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.InputModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TextCoreFontEngineModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Physics2DModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Net.Http/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Internal/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Profiling.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Numerics/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Runtime.Serialization/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Data/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Xml.Linq/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TextCoreTextEngineModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Burst/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.2D.Common.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AndroidJNIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Purchasing.SecurityStub/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Purchasing.AppleCore/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Purchasing.SecurityCore/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppPurchasing.Common/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Purchasing.WinRTCore/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.PropertiesModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.HierarchyCoreModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.InputForUIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.JSONSerializeModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "WebSocketDotNet/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.Backports/1.1.2.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Diagnostics.NETCore.Client/0.2.8.10101": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.SubsystemsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.ILHelpers/1.1.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}