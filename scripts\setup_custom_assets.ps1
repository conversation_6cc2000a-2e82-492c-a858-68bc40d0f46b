$ErrorActionPreference = 'Stop'
$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$resources = Join-Path $root 'Resources'
if (-not (Test-Path -LiteralPath $resources)) { New-Item -ItemType Directory -Path $resources | Out-Null }

# 1x1 PNG placeholders (blue for icon, orange for portrait). Replace these files with your custom art later.
$png1x1 = {
 param([string]$hex)
 $bytes = [byte[]](
   137,80,78,71,13,10,26,10,0,0,0,13,73,72,68,82,
   0,0,0,1,0,0,0,1,8,6,0,0,0,31,21,196,137,
   0,0,0,13,73,68,65,84,8,215,99,248,15,4,0,9,251,3,253,
   0,0,0,1,115,82,71,66,0,$(([Convert]::ToInt32($hex.Substring(0,2),16))),$(([Convert]::ToInt32($hex.Substring(2,2),16))),$(([Convert]::ToInt32($hex.Substring(4,2),16))),0,
   0,0,0,0,73,69,78,68,174,66,96,130)
 return $bytes
}

[IO.File]::WriteAllBytes((Join-Path $resources 'LightningMonkey-Icon.png'), (& $png1x1 '3a7bd5'))   # blue
[IO.File]::WriteAllBytes((Join-Path $resources 'LightningMonkey-Portrait.png'), (& $png1x1 'ff6a00')) # orange

# Ensure ModTower specifies custom Icon/Portrait
$lt = Join-Path $root 'Towers\LightningMonkey.cs'
if (-not (Test-Path -LiteralPath $lt)) { throw "Missing file: $lt" }
$src = Get-Content -Raw -LiteralPath $lt
if ($src -notmatch 'public\s+override\s+string\s+Icon') {
  $src = $src -replace '(public\s+override\s+int\s+Cost\s*=>\s*\d+;)', "$1`n`n        public override string Icon => \"LightningMonkey-Icon\";`n        public override string Portrait => \"LightningMonkey-Portrait\";"
}
Set-Content -LiteralPath $lt -Value $src -Encoding UTF8

# Ensure csproj embeds Resources/*.png
$csproj = Join-Path $root 'LightningMonkey.csproj'
[xml]$doc = Get-Content -Raw -LiteralPath $csproj
$itemGroups = @($doc.Project.ItemGroup)
$hasEmbed = $false
foreach ($ig in $itemGroups) {
  foreach ($n in $ig.ChildNodes) {
    if ($n.Name -eq 'EmbeddedResource' -and $n.Include -like 'Resources*') { $hasEmbed = $true }
  }
}
if (-not $hasEmbed) {
  $ig = $doc.CreateElement('ItemGroup')
  $er = $doc.CreateElement('EmbeddedResource')
  $er.SetAttribute('Include','Resources\\**\\*.png')
  [void]$ig.AppendChild($er)
  [void]$doc.Project.AppendChild($ig)
}
$doc.Save($csproj)
Write-Host 'Custom icon/portrait added and csproj updated to embed resources.'

