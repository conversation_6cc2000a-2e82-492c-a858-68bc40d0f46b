$ErrorActionPreference = 'Stop'

# Paths
$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$allUpgrades = Join-Path $root 'Towers\Upgrades\AllUpgrades.cs'
$chainLightning = Join-Path $root 'Towers\Upgrades\ChainLightning.cs'

# Write AllUpgrades.cs with visual display progression on each tier
$allUpgradesContent = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  // Top Path
  public class DarkBoost : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => TOP; public override int Tier => 1; public override int Cost => 250;
    public override string DisplayName => "Dark Boost"; public override string Description => "+1 damage, +2 pierce.";
    public override void ApplyUpgrade(TowerModel t) {
      var p=t.GetAttackModel().weapons[0].projectile; p.GetDamageModel().damage+=1; p.pierce+=2;
      var wiz200 = Game.instance.model.GetTowerFromId("WizardMonkey-200"); if (wiz200 != null) t.display = wiz200.display;
    }
  }
  public class ShadowStrike : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => TOP; public override int Tier => 2; public override int Cost => 500;
    public override string DisplayName => "Shadow Strike"; public override string Description => "15% faster.";
    public override void ApplyUpgrade(TowerModel t) {
      t.GetAttackModel().weapons[0].Rate *= 0.85f; var wiz300 = Game.instance.model.GetTowerFromId("WizardMonkey-300"); if (wiz300 != null) t.display = wiz300.display;
    }
  }
  public class VoidChanneling : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => TOP; public override int Tier => 3; public override int Cost => 1200;
    public override string DisplayName => "Void Channeling"; public override string Description => "+2 dmg, +3 pierce.";
    public override void ApplyUpgrade(TowerModel t) {
      var p=t.GetAttackModel().weapons[0].projectile; p.GetDamageModel().damage+=2; p.pierce+=3;
      var wiz302 = Game.instance.model.GetTowerFromId("WizardMonkey-302"); if (wiz302 != null) t.display = wiz302.display;
    }
  }
  public class DarkMastery : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => TOP; public override int Tier => 4; public override int Cost => 9000;
    public override string DisplayName => "Dark Mastery"; public override string Description => "Bigger bolts and faster cast.";
    public override void ApplyUpgrade(TowerModel t) {
      var a=t.GetAttackModel(); a.weapons[0].Rate *= 0.75f; a.weapons[0].projectile.scale *= 1.25f;
      var druid400 = Game.instance.model.GetTowerFromId("Druid-400"); if (druid400 != null) t.display = druid400.display;
    }
  }
  public class VoidStormMaster : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => TOP; public override int Tier => 5; public override int Cost => 45000;
    public override string DisplayName => "Void Storm Master"; public override string Description => "Amplified void power.";
    public override void ApplyUpgrade(TowerModel tower) {
      var attack = tower.GetAttackModel(); var proj = attack.weapons[0].projectile;
      proj.GetDamageModel().damage += 10f; proj.pierce += 10f; attack.weapons[0].Rate *= 0.6f;
      var wiz520 = Game.instance.model.GetTowerFromId("WizardMonkey-520"); if (wiz520 != null) tower.display = wiz520.display;
    }
  }

  // Middle Path
  public class QuickCast : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => MIDDLE; public override int Tier => 1; public override int Cost => 150;
    public override string DisplayName => "Quick Cast"; public override string Description => "15% faster";
    public override void ApplyUpgrade(TowerModel t) {
      t.GetAttackModel().weapons[0].Rate *= 0.85f; var druid100 = Game.instance.model.GetTowerFromId("Druid-100"); if (druid100 != null) t.display = druid100.display;
    }
  }
  public class SurgeCasting : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => MIDDLE; public override int Tier => 2; public override int Cost => 300;
    public override string DisplayName => "Surge Casting"; public override string Description => "20% faster";
    public override void ApplyUpgrade(TowerModel t) {
      t.GetAttackModel().weapons[0].Rate *= 0.8f; var druid200 = Game.instance.model.GetTowerFromId("Druid-200"); if (druid200 != null) t.display = druid200.display;
    }
  }

  // Bottom Path
  public class ExtendedRange : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => BOTTOM; public override int Tier => 1; public override int Cost => 180;
    public override string DisplayName => "Extended Range"; public override string Description => "+10 range";
    public override void ApplyUpgrade(TowerModel t) {
      t.range += 10f; var druid010 = Game.instance.model.GetTowerFromId("Druid-010"); if (druid010 != null) t.display = druid010.display;
    }
  }
  public class PrecisionStrike : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => BOTTOM; public override int Tier => 2; public override int Cost => 350;
    public override string DisplayName => "Precision Strike"; public override string Description => "+1 dmg, +8 range";
    public override void ApplyUpgrade(TowerModel t) {
      t.range += 8f; t.GetAttackModel().weapons[0].projectile.GetDamageModel().damage += 1; var druid020 = Game.instance.model.GetTowerFromId("Druid-020"); if (druid020 != null) t.display = druid020.display;
    }
  }
}
'@
Set-Content -Encoding UTF8 -LiteralPath $allUpgrades -Value $allUpgradesContent

# Patch ChainLightning.cs to also set a display on T3
$chainLightningContent = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  public class ChainLightning : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => MIDDLE;
    public override int Tier => 3;
    public override int Cost => 1500;
    public override string DisplayName => "Chain Lightning";
    public override void ApplyUpgrade(TowerModel towerModel) {
      var proj = towerModel.GetAttackModel().weapons[0].projectile;
      var lm = proj.GetBehavior<LightningModel>();
      if (lm != null) {
        lm.splits += 12;
        lm.splitRange += 18f;
      } else {
        var druid200 = Game.instance.model.GetTowerFromId("Druid-200");
        var src = druid200.GetAttackModel().weapons[0].projectile.GetBehavior<LightningModel>().Duplicate();
        proj.AddBehavior(src);
      }
      proj.pierce += 2;
      proj.GetDamageModel().damage += 1;
      var druid300 = Game.instance.model.GetTowerFromId("Druid-300"); if (druid300 != null) towerModel.display = druid300.display;
    }
  }
}
'@
Set-Content -Encoding UTF8 -LiteralPath $chainLightning -Value $chainLightningContent

Write-Host 'Patched AllUpgrades.cs and ChainLightning.cs with visual display changes.'

