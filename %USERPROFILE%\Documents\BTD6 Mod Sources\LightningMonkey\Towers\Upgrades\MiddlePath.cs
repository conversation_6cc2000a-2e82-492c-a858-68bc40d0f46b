using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;

namespace LightningMonkey.Towers.Upgrades
{
    // Middle Path - Chain Lightning/Multi-target Focus
    
    public class StaticCharge : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => MIDDLE;
        public override int Tier => 1;
        public override int Cost => 300;
        public override string DisplayName => "Static Charge";
        public override string Description => "Lightning builds up static charge. +1 pierce, slightly faster attacks.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.9f; // 10% faster
            weapon.projectile.pierce += 1;
        }
    }

    public class ArcLightning : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => MIDDLE;
        public override int Tier => 2;
        public override int Cost => 650;
        public override string DisplayName => "Arc Lightning";
        public override string Description => "Lightning arcs to nearby bloons. +2 pierce, creates small chain effect.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var projectile = towerModel.GetAttackModel().weapons[0].projectile;
            projectile.pierce += 2;
            
            // Add basic chain behavior
            var chainProjectile = projectile.Duplicate();
            chainProjectile.pierce = 2f;
            chainProjectile.GetDamageModel().damage *= 0.8f; // Reduced chain damage
            chainProjectile.scale = 0.8f;
            
            projectile.AddBehavior(new CreateProjectileOnContactModel(
                "ArcLightning",
                chainProjectile,
                new SingleEmissionModel("", null),
                true, false, false
            ));
        }
    }

    public class ChainLightning : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => MIDDLE;
        public override int Tier => 3;
        public override int Cost => 1500;
        public override string DisplayName => "Chain Lightning";
        public override string Description => "Lightning chains between multiple bloons! +1 damage, +3 pierce, enhanced chaining.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var projectile = towerModel.GetAttackModel().weapons[0].projectile;
            projectile.GetDamageModel().damage += 1;
            projectile.pierce += 3;
            
            // Enhanced chain behavior
            var chainProjectile = projectile.Duplicate();
            chainProjectile.pierce = 4f;
            chainProjectile.GetDamageModel().damage *= 0.75f; // Better chain damage
            chainProjectile.scale = 0.9f;
            
            // Remove old chain behavior and add new one
            projectile.RemoveBehavior<CreateProjectileOnContactModel>();
            projectile.AddBehavior(new CreateProjectileOnContactModel(
                "ChainLightning",
                chainProjectile,
                new SingleEmissionModel("", null),
                true, false, false
            ));
        }
    }

    public class LightningWeb : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => MIDDLE;
        public override int Tier => 4;
        public override int Cost => 7200;
        public override string DisplayName => "Lightning Web";
        public override string Description => "Creates a web of lightning that chains extensively! +2 damage, +4 pierce, multiple chains.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var projectile = towerModel.GetAttackModel().weapons[0].projectile;
            projectile.GetDamageModel().damage += 2;
            projectile.pierce += 4;
            
            // Multiple chain behavior
            var chainProjectile = projectile.Duplicate();
            chainProjectile.pierce = 6f;
            chainProjectile.GetDamageModel().damage *= 0.7f;
            chainProjectile.scale = 1.0f;
            
            // Create multiple chain projectiles
            projectile.RemoveBehavior<CreateProjectileOnContactModel>();
            for (int i = 0; i < 3; i++)
            {
                projectile.AddBehavior(new CreateProjectileOnContactModel(
                    $"LightningWeb_{i}",
                    chainProjectile,
                    new SingleEmissionModel("", null),
                    true, false, false
                ));
            }
        }
    }

    public class StormNetwork : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => MIDDLE;
        public override int Tier => 5;
        public override int Cost => 28000;
        public override string DisplayName => "Storm Network";
        public override string Description => "Creates a massive network of lightning that chains across the entire map! +3 damage, unlimited pierce on chains.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            towerModel.range += 20f;
            var projectile = towerModel.GetAttackModel().weapons[0].projectile;
            projectile.GetDamageModel().damage += 3;
            projectile.pierce += 6;
            
            // Massive chain network
            var chainProjectile = projectile.Duplicate();
            chainProjectile.pierce = 999f; // Unlimited pierce on chains
            chainProjectile.GetDamageModel().damage *= 0.6f;
            chainProjectile.scale = 1.2f;
            
            // Create extensive chain network
            projectile.RemoveBehaviors<CreateProjectileOnContactModel>();
            for (int i = 0; i < 5; i++)
            {
                projectile.AddBehavior(new CreateProjectileOnContactModel(
                    $"StormNetwork_{i}",
                    chainProjectile,
                    new SingleEmissionModel("", null),
                    true, false, false
                ));
            }
        }
    }
}
