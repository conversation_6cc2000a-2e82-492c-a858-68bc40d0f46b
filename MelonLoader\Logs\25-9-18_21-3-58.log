
[21:03:59.214] ------------------------------
[21:03:59.216] MelonLoader v0.7.1 Open-Beta
[21:03:59.217] OS: Windows 11
[21:03:59.218] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[21:03:59.218] ------------------------------
[21:03:59.219] Game Type: Il2cpp
[21:03:59.219] Game Arch: x64
[21:03:59.219] ------------------------------
[21:03:59.219] Command-Line: 
[21:03:59.219] ------------------------------
[21:03:59.219] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:03:59.220] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:03:59.220] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[21:03:59.220] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[21:03:59.220] Runtime Type: net6
[21:03:59.286] ------------------------------
[21:03:59.287] Game Name: BloonsTD6
[21:03:59.287] Game Developer: Ninja Kiwi
[21:03:59.289] Unity Version: 6000.0.52f1
[21:03:59.289] Game Version: 50.2
[21:03:59.289] ------------------------------

[21:03:59.647] Preferences Loaded!

[21:03:59.659] Loading UserLibs...
[21:03:59.661] 0 UserLibs loaded.

[21:03:59.661] Loading Plugins...
[21:03:59.665] 0 Plugins loaded.

[21:04:00.072] Loading Il2CppAssemblyGenerator...
[21:04:00.110] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[21:04:00.290] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[21:04:00.290] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[21:04:00.291] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[21:04:00.291] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[21:04:00.296] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[21:04:00.296] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[21:04:00.296] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[21:04:00.297] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[21:04:00.297] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[21:04:00.297] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[21:04:00.297] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[21:04:00.297] [Il2CppAssemblyGenerator] Checking GameAssembly...
[21:04:00.472] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[21:04:00.473] Loading Mods...
[21:04:00.520] ------------------------------
[21:04:00.556] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[21:04:00.556] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[21:04:00.558] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[21:04:00.558] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[21:04:00.562] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[21:04:00.562] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[21:04:00.651] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[21:04:00.651] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[21:04:00.662] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[21:04:00.662] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[21:04:01.297] ------------------------------
[21:04:01.297] BloonsTD6 Mod Helper v3.4.12
[21:04:01.297] by Gurrenm4 and Doombubbles
[21:04:01.297] Assembly: Btd6ModHelper.dll
[21:04:01.297] ------------------------------
[21:04:01.301] ------------------------------
[21:04:01.301] Ultimate Crosspathing v1.7.1
[21:04:01.302] by doombubbles
[21:04:01.302] Assembly: UltimateCrosspathing.dll
[21:04:01.302] ------------------------------
[21:04:01.305] ------------------------------
[21:04:01.305] Faster Forward v1.1.5
[21:04:01.305] by doombubbles
[21:04:01.305] Assembly: FasterForward.dll
[21:04:01.305] ------------------------------
[21:04:01.309] ------------------------------
[21:04:01.309] Powers in Shop v3.0.3
[21:04:01.309] by doombubbles
[21:04:01.309] Assembly: PowersInShop.dll
[21:04:01.309] ------------------------------
[21:04:01.312] ------------------------------
[21:04:01.312] Unlimited 5th Tiers + v1.1.9
[21:04:01.312] by doombubbles
[21:04:01.313] Assembly: Unlimited5thTiers.dll
[21:04:01.313] ------------------------------
[21:04:01.313] ------------------------------
[21:04:01.313] 5 Mods loaded.

[21:04:02.395] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[21:04:02.576] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[21:04:02.600] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[21:04:02.604] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[21:04:02.616] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[21:04:02.618] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[21:04:03.094] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[21:04:03.659] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[21:04:04.555] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[21:04:04.652] [BloonsTD6_Mod_Helper] AlchemistLoader finished loading bytes
[21:04:04.725] [BloonsTD6_Mod_Helper] BananaFarmLoader finished loading bytes
[21:04:04.758] [BloonsTD6_Mod_Helper] BananaFarmerProLoader finished loading bytes
[21:04:04.983] [BloonsTD6_Mod_Helper] BeastHandlerLoader finished loading bytes
[21:04:05.077] [BloonsTD6_Mod_Helper] BombShooterLoader finished loading bytes
[21:04:05.136] [BloonsTD6_Mod_Helper] BoomerangMonkeyLoader finished loading bytes
[21:04:05.202] [BloonsTD6_Mod_Helper] DartMonkeyLoader finished loading bytes
[21:04:05.381] [BloonsTD6_Mod_Helper] DartlingGunnerLoader finished loading bytes
[21:04:05.468] [BloonsTD6_Mod_Helper] DesperadoLoader finished loading bytes
[21:04:05.668] [BloonsTD6_Mod_Helper] DruidLoader finished loading bytes
[21:04:05.854] [BloonsTD6_Mod_Helper] EngineerMonkeyLoader finished loading bytes
[21:04:05.960] [BloonsTD6_Mod_Helper] GlueGunnerLoader finished loading bytes
[21:04:06.112] [BloonsTD6_Mod_Helper] HeliPilotLoader finished loading bytes
[21:04:06.243] [BloonsTD6_Mod_Helper] IceMonkeyLoader finished loading bytes
[21:04:06.494] [BloonsTD6_Mod_Helper] MermonkeyLoader finished loading bytes
[21:04:06.633] [BloonsTD6_Mod_Helper] MonkeyAceLoader finished loading bytes
[21:04:06.768] [BloonsTD6_Mod_Helper] MonkeyBuccaneerLoader finished loading bytes
[21:04:06.901] [BloonsTD6_Mod_Helper] MonkeySubLoader finished loading bytes
[21:04:06.974] [BloonsTD6_Mod_Helper] MonkeyVillageLoader finished loading bytes
[21:04:07.080] [BloonsTD6_Mod_Helper] MortarMonkeyLoader finished loading bytes
[21:04:07.137] [BloonsTD6_Mod_Helper] NinjaMonkeyLoader finished loading bytes
[21:04:07.265] [BloonsTD6_Mod_Helper] SniperMonkeyLoader finished loading bytes
[21:04:07.356] [BloonsTD6_Mod_Helper] SpikeFactoryLoader finished loading bytes
[21:04:07.387] [BloonsTD6_Mod_Helper] SuperMonkeyBeaconLoader finished loading bytes
[21:04:07.882] [BloonsTD6_Mod_Helper] SuperMonkeyLoader finished loading bytes
[21:04:07.932] [BloonsTD6_Mod_Helper] TackShooterLoader finished loading bytes
[21:04:08.093] [BloonsTD6_Mod_Helper] WizardMonkeyLoader finished loading bytes
[21:04:10.740] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 356 mods over 7.1 seconds
[21:04:13.443] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[21:04:13.446] [BloonsTD6_Mod_Helper] Registering ModContent for Ultimate Crosspathing...
[21:04:13.494] [Ultimate_Crosspathing] Finished loading DartMonkeys!
[21:04:13.543] [Ultimate_Crosspathing] Finished loading BoomerangMonkeys!
[21:04:13.595] [Ultimate_Crosspathing] Finished loading BombShooters!
[21:04:13.620] [Ultimate_Crosspathing] Finished loading TackShooters!
[21:04:13.673] [Ultimate_Crosspathing] Finished loading IceMonkeys!
[21:04:13.731] [Ultimate_Crosspathing] Finished loading GlueGunners!
[21:04:13.768] [Ultimate_Crosspathing] Finished loading Desperados!
[21:04:13.793] [Ultimate_Crosspathing] Finished loading SniperMonkeys!
[21:04:13.934] [Ultimate_Crosspathing] Finished loading MonkeySubs!
[21:04:13.996] [Ultimate_Crosspathing] Finished loading MonkeyBuccaneers!
[21:04:14.056] [Ultimate_Crosspathing] Finished loading MonkeyAces!
[21:04:14.105] [Ultimate_Crosspathing] Finished loading HeliPilots!
[21:04:14.132] [Ultimate_Crosspathing] Finished loading MortarMonkeys!
[21:04:14.186] [Ultimate_Crosspathing] Finished loading DartlingGunners!
[21:04:14.234] [Ultimate_Crosspathing] Finished loading WizardMonkeys!
[21:04:14.343] [Ultimate_Crosspathing] Finished loading SuperMonkeys!
[21:04:14.375] [Ultimate_Crosspathing] Finished loading NinjaMonkeys!
[21:04:14.431] [Ultimate_Crosspathing] Finished loading Alchemists!
[21:04:14.484] [Ultimate_Crosspathing] Finished loading Druids!
[21:04:14.570] [Ultimate_Crosspathing] Finished loading Mermonkeys!
[21:04:14.606] [Ultimate_Crosspathing] Finished loading BananaFarms!
[21:04:14.666] [Ultimate_Crosspathing] Finished loading SpikeFactorys!
[21:04:14.722] [Ultimate_Crosspathing] Finished loading MonkeyVillages!
[21:04:14.767] [Ultimate_Crosspathing] Finished loading EngineerMonkeys!
[21:04:14.817] [Ultimate_Crosspathing] Finished loading BeastHandlers!
[21:04:14.828] [Ultimate_Crosspathing] Finished loading BananaFarmerPros!
[21:04:14.847] [Ultimate_Crosspathing] Finished loading SuperMonkeyBeacons!
[21:04:14.848] [BloonsTD6_Mod_Helper] Registering ModContent for Powers in Shop...
[21:04:14.907] [BloonsTD6_Mod_Helper] Registering ModContent for Unlimited 5th Tiers +...
[21:05:47.818] Preferences Saved!
