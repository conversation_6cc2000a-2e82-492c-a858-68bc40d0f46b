$ErrorActionPreference = 'Stop'

$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$lt = Join-Path $root 'Towers\LightningMonkey.cs'
$csproj = Join-Path $root 'LightningMonkey.csproj'
$resDir = Join-Path $root 'Resources'

if (-not (Test-Path -LiteralPath $lt)) { throw "Missing file: $lt" }
if (-not (Test-Path -LiteralPath $csproj)) { throw "Missing file: $csproj" }
if (-not (Test-Path -LiteralPath $resDir)) { New-Item -ItemType Directory -Path $resDir | Out-Null }

# Create simple placeholder PNGs (replace with real art later)
$pngBlue = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGP4////fwAJ+wP9AAAAAElFTkSuQmCC'
$pngOrange = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGP4//8/AwMDgAEAAZ6fBVoAAAAASUVORK5CYII='
[IO.File]::WriteAllBytes((Join-Path $resDir 'LightningMonkey-Icon.png'), [Convert]::FromBase64String($pngBlue))
[IO.File]::WriteAllBytes((Join-Path $resDir 'LightningMonkey-Portrait.png'), [Convert]::FromBase64String($pngOrange))

# Insert Icon/Portrait overrides after Cost property if not present
$content = Get-Content -LiteralPath $lt
if (-not ($content -match 'public\s+override\s+string\s+Icon')) {
  $idx = ($content | Select-String -Pattern 'public\s+override\s+int\s+Cost' -SimpleMatch:$false | Select-Object -First 1).LineNumber
  if (-not $idx) { throw 'Could not find Cost property in LightningMonkey.cs' }
  $before = $content[0..($idx-1)]
  $after = $content[$idx..($content.Length-1)]
  $inserts = @(
    '        public override string Icon => "LightningMonkey-Icon";',
    '        public override string Portrait => "LightningMonkey-Portrait";'
  )
  $new = @()
  $new += $before
  $new += $inserts
  $new += $after
  Set-Content -LiteralPath $lt -Value $new -Encoding UTF8
}

# Ensure PNGs are embedded in csproj
[xml]$doc = Get-Content -Raw -LiteralPath $csproj
$hasEmbed = $false
foreach ($ig in $doc.Project.ItemGroup) {
  foreach ($n in $ig.ChildNodes) { if ($n.Name -eq 'EmbeddedResource' -and $n.Include -like 'Resources*') { $hasEmbed = $true } }
}
if (-not $hasEmbed) {
  $ig = $doc.CreateElement('ItemGroup')
  $er = $doc.CreateElement('EmbeddedResource')
  $er.SetAttribute('Include','Resources\\**\\*.png')
  [void]$ig.AppendChild($er)
  [void]$doc.Project.AppendChild($ig)
  $doc.Save($csproj)
}

Write-Host 'Custom icon and portrait set; csproj embeds resources.'

