@echo off
echo Fixing GetTowerFromId calls...

set "TOWER_FILE=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\LightningMonkey.cs"
set "FORKED_FILE=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\Upgrades\Mid_T3_ForkedLightning.cs"

echo Fixing tower file...
powershell -NoProfile -Command "(Get-Content '%TOWER_FILE%' -Raw) -replace 'm\.GetTowerFromId','Game.instance.model.GetTowerFromId' | Set-Content '%TOWER_FILE%'"

echo Fixing forked lightning upgrade...
powershell -NoProfile -Command "(Get-Content '%FORKED_FILE%' -Raw) -replace 'm\.GetTowerFromId','Game.instance.model.GetTowerFromId' | Set-Content '%FORKED_FILE%'"

echo Building final version...
cd /d "%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey mod is ready!
    echo ========================================
    echo.
    echo The mod has been built and copied to your Mods folder.
    echo Launch BTD6 to see your new Lightning Monkey in the Magic category.
    echo.
    echo Lightning Monkey Features:
    echo - Base cost: 550 (Magic tower)
    echo - Uses lightning projectiles that chain through bloons
    echo - 15 upgrades across 3 paths (5/5/5):
    echo.
    echo   TOP PATH (Damage/Pierce):
    echo   T1: Charged Bolts - +1 damage, +1 pierce
    echo   T2: Supercharged - 15%% faster attacks
    echo   T3: Thunderhead - +1 damage, +2 pierce
    echo   T4: Tempest Strike - +2 damage, +2 pierce, 10%% faster
    echo   T5: Stormlord - +3 damage, +4 pierce, 20%% faster
    echo.
    echo   MIDDLE PATH (Speed/Multi-shot):
    echo   T1: Quick Channeling - 10%% faster
    echo   T2: Surge Casting - Another 15%% faster
    echo   T3: Forked Lightning - Fires 3 bolts in spread
    echo   T4: Chain Mastery - +2 pierce, 10%% faster
    echo   T5: Maelstrom Conduit - +2 damage, +3 pierce, 25%% faster
    echo.
    echo   BOTTOM PATH (Range/Precision):
    echo   T1: Static Field - +8 range
    echo   T2: Ion Focus - 10%% faster
    echo   T3: Sky Reach - +10 range, +1 damage
    echo   T4: Storm Sight - +8 range, 10%% faster
    echo   T5: Zeus Vision - +12 range, +2 damage
    echo.
    echo Enjoy your new Lightning Monkey!
) else (
    echo Build failed. Check errors above.
)
pause
