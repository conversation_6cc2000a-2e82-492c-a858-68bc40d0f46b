using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;

namespace LightningMonkey.Towers.Upgrades
{
    // Top Path - Power/Damage Focus
    
    public class ChargedBolts : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => TOP;
        public override int Tier => 1;
        public override int Cost => 350;
        public override string DisplayName => "Charged Bolts";
        public override string Description => "Lightning bolts are supercharged with more electrical energy. +1 damage, +1 pierce.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var projectile = towerModel.GetAttackModel().weapons[0].projectile;
            projectile.GetDamageModel().damage += 1;
            projectile.pierce += 1;
        }
    }

    public class PowerSurge : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => TOP;
        public override int Tier => 2;
        public override int Cost => 750;
        public override string DisplayName => "Power Surge";
        public override string Description => "Electrical surges increase attack speed and damage. +1 damage, 15% faster attacks.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.85f; // 15% faster
            weapon.projectile.GetDamageModel().damage += 1;
        }
    }

    public class LightningStorm : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => TOP;
        public override int Tier => 3;
        public override int Cost => 1800;
        public override string DisplayName => "Lightning Storm";
        public override string Description => "Unleashes a storm of lightning! +2 damage, +2 pierce, 20% faster attacks.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.8f; // 20% faster
            var projectile = weapon.projectile;
            projectile.GetDamageModel().damage += 2;
            projectile.pierce += 2;
            projectile.scale *= 1.3f; // Bigger lightning bolts
        }
    }

    public class TempestMaster : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => TOP;
        public override int Tier => 4;
        public override int Cost => 6500;
        public override string DisplayName => "Tempest Master";
        public override string Description => "Master of electrical tempests! +3 damage, +3 pierce, 25% faster attacks, +10 range.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            towerModel.range += 10f;
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.75f; // 25% faster
            var projectile = weapon.projectile;
            projectile.GetDamageModel().damage += 3;
            projectile.pierce += 3;
            projectile.scale *= 1.4f;
        }
    }

    public class LordOfLightning : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => TOP;
        public override int Tier => 5;
        public override int Cost => 32000;
        public override string DisplayName => "Lord of Lightning";
        public override string Description => "Ultimate master of lightning! Massive damage and speed increase. +5 damage, +5 pierce, 40% faster attacks.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            towerModel.range += 15f;
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.6f; // 40% faster
            var projectile = weapon.projectile;
            projectile.GetDamageModel().damage += 5;
            projectile.pierce += 5;
            projectile.scale *= 1.8f; // Massive lightning bolts
        }
    }
}
