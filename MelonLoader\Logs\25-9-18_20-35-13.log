
[20:35:14.176] ------------------------------
[20:35:14.178] MelonLoader v0.7.1 Open-Beta
[20:35:14.179] OS: Windows 11
[20:35:14.179] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[20:35:14.179] ------------------------------
[20:35:14.180] Game Type: Il2cpp
[20:35:14.180] Game Arch: x64
[20:35:14.180] ------------------------------
[20:35:14.180] Command-Line: 
[20:35:14.180] ------------------------------
[20:35:14.180] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[20:35:14.181] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[20:35:14.181] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[20:35:14.181] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[20:35:14.181] Runtime Type: net6
[20:35:14.248] ------------------------------
[20:35:14.248] Game Name: BloonsTD6
[20:35:14.248] Game Developer: Ninja Kiwi
[20:35:14.250] Unity Version: 6000.0.52f1
[20:35:14.250] Game Version: 50.2
[20:35:14.250] ------------------------------

[20:35:14.640] Preferences Loaded!

[20:35:14.650] Loading UserLibs...
[20:35:14.653] 0 UserLibs loaded.

[20:35:14.654] Loading Plugins...
[20:35:14.658] 0 Plugins loaded.

[20:35:15.034] Loading Il2CppAssemblyGenerator...
[20:35:15.071] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[20:35:15.235] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[20:35:15.236] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[20:35:15.236] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[20:35:15.236] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[20:35:15.243] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[20:35:15.243] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[20:35:15.243] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[20:35:15.243] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[20:35:15.244] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[20:35:15.244] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[20:35:15.244] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[20:35:15.244] [Il2CppAssemblyGenerator] Checking GameAssembly...
[20:35:15.433] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[20:35:15.434] Loading Mods...
[20:35:15.487] ------------------------------
[20:35:15.528] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[20:35:15.528] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[20:35:15.531] Melon Assembly loaded: '.\Mods\EditPlayerData.dll'
[20:35:15.531] SHA256 Hash: 'D0AE1A904057FF46AF12EA6A6856E28954B9DE4191999802288DA1DD00C4483D'
[20:35:15.532] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[20:35:15.532] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[20:35:15.538] Melon Assembly loaded: '.\Mods\IndustrialFarmer.dll'
[20:35:15.539] SHA256 Hash: '55C6358F421E23428D41BA902A7B3DC9D22F626234AD9CC3BAE27C032087F2D3'
[20:35:15.540] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[20:35:15.540] SHA256 Hash: 'C23277E37C12E91D3C47EB6A2548626CCE3111679FC4770DDEA2C06B5B77C9E9'
[20:35:15.545] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[20:35:15.545] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[20:35:15.630] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[20:35:15.630] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[20:35:15.641] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[20:35:15.641] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[20:35:16.213] ------------------------------
[20:35:16.214] BloonsTD6 Mod Helper v3.4.12
[20:35:16.214] by Gurrenm4 and Doombubbles
[20:35:16.214] Assembly: Btd6ModHelper.dll
[20:35:16.214] ------------------------------
[20:35:16.217] ------------------------------
[20:35:16.218] Ultimate Crosspathing v1.7.1
[20:35:16.218] by doombubbles
[20:35:16.218] Assembly: UltimateCrosspathing.dll
[20:35:16.218] ------------------------------
[20:35:16.223] ------------------------------
[20:35:16.223] EditPlayerData v1.5.1
[20:35:16.223] by MaliciousFiles
[20:35:16.223] Assembly: EditPlayerData.dll
[20:35:16.224] ------------------------------
[20:35:16.226] ------------------------------
[20:35:16.226] Faster Forward v1.1.5
[20:35:16.227] by doombubbles
[20:35:16.227] Assembly: FasterForward.dll
[20:35:16.227] ------------------------------
[20:35:16.230] ------------------------------
[20:35:16.230] Industrial Farmer v1.0.15
[20:35:16.230] by doombubbles
[20:35:16.230] Assembly: IndustrialFarmer.dll
[20:35:16.230] ------------------------------
[20:35:16.233] ------------------------------
[20:35:16.233] Lightning Monkey Mod v1.0.0
[20:35:16.233] by You
[20:35:16.233] Assembly: LightningMonkey.dll
[20:35:16.233] ------------------------------
[20:35:16.237] ------------------------------
[20:35:16.238] Powers in Shop v3.0.3
[20:35:16.238] by doombubbles
[20:35:16.238] Assembly: PowersInShop.dll
[20:35:16.238] ------------------------------
[20:35:16.241] ------------------------------
[20:35:16.241] Unlimited 5th Tiers + v1.1.9
[20:35:16.241] by doombubbles
[20:35:16.241] Assembly: Unlimited5thTiers.dll
[20:35:16.241] ------------------------------
[20:35:16.241] ------------------------------
[20:35:16.242] 8 Mods loaded.

[20:35:17.309] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[20:35:17.483] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[20:35:17.509] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[20:35:17.513] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[20:35:17.528] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[20:35:17.530] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[20:35:17.628] [Il2CppInterop] Method Void SetNumColumns(Int32, Int32[]) on type EditPlayerData.UI.ModHelperTable has unsupported parameter Int32[] colFlex of type System.Int32[]
[20:35:17.630] [Il2CppInterop] Method Void SetSetting(EditPlayerData.UI.PlayerDataSetting) on type EditPlayerData.UI.PlayerDataSettingDisplay has unsupported parameter EditPlayerData.UI.PlayerDataSetting setting of type EditPlayerData.UI.PlayerDataSetting
[20:35:18.043] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[20:35:18.490] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[20:35:18.755] [EditPlayerData] Melon Assembly loaded: 'C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Windows.Forms.dll'
[20:35:18.755] [EditPlayerData] SHA256 Hash: 'F8EE5BFDC1FE7508773CEB997F6C78FC8CF8CDBAE11F0DEB15E78AA94BEBFED7'
[20:35:18.757] [EditPlayerData] EditPlayerData loaded!
[20:35:18.889] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[20:35:19.538] [BloonsTD6_Mod_Helper] AlchemistLoader finished loading bytes
[20:35:19.609] [BloonsTD6_Mod_Helper] BananaFarmLoader finished loading bytes
[20:35:19.645] [BloonsTD6_Mod_Helper] BananaFarmerProLoader finished loading bytes
[20:35:19.843] [BloonsTD6_Mod_Helper] BeastHandlerLoader finished loading bytes
[20:35:19.944] [BloonsTD6_Mod_Helper] BombShooterLoader finished loading bytes
[20:35:20.006] [BloonsTD6_Mod_Helper] BoomerangMonkeyLoader finished loading bytes
[20:35:20.085] [BloonsTD6_Mod_Helper] DartMonkeyLoader finished loading bytes
[20:35:20.292] [BloonsTD6_Mod_Helper] DartlingGunnerLoader finished loading bytes
[20:35:20.395] [BloonsTD6_Mod_Helper] DesperadoLoader finished loading bytes
[20:35:20.509] [BloonsTD6_Mod_Helper] DruidLoader finished loading bytes
[20:35:20.700] [BloonsTD6_Mod_Helper] EngineerMonkeyLoader finished loading bytes
[20:35:20.795] [BloonsTD6_Mod_Helper] GlueGunnerLoader finished loading bytes
[20:35:20.980] [BloonsTD6_Mod_Helper] HeliPilotLoader finished loading bytes
[20:35:21.151] [BloonsTD6_Mod_Helper] IceMonkeyLoader finished loading bytes
[20:35:21.387] [BloonsTD6_Mod_Helper] MermonkeyLoader finished loading bytes
[20:35:21.493] [BloonsTD6_Mod_Helper] MonkeyAceLoader finished loading bytes
[20:35:21.721] [BloonsTD6_Mod_Helper] MonkeyBuccaneerLoader finished loading bytes
[20:35:21.848] [BloonsTD6_Mod_Helper] MonkeySubLoader finished loading bytes
[20:35:21.920] [BloonsTD6_Mod_Helper] MonkeyVillageLoader finished loading bytes
[20:35:22.022] [BloonsTD6_Mod_Helper] MortarMonkeyLoader finished loading bytes
[20:35:22.172] [BloonsTD6_Mod_Helper] NinjaMonkeyLoader finished loading bytes
[20:35:22.237] [BloonsTD6_Mod_Helper] SniperMonkeyLoader finished loading bytes
[20:35:22.337] [BloonsTD6_Mod_Helper] SpikeFactoryLoader finished loading bytes
[20:35:22.374] [BloonsTD6_Mod_Helper] SuperMonkeyBeaconLoader finished loading bytes
[20:35:22.762] [BloonsTD6_Mod_Helper] SuperMonkeyLoader finished loading bytes
[20:35:22.843] [BloonsTD6_Mod_Helper] TackShooterLoader finished loading bytes
[20:35:23.034] [BloonsTD6_Mod_Helper] WizardMonkeyLoader finished loading bytes
[20:35:25.195] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 356 mods over 6.7 seconds
[20:35:28.729] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[20:35:28.732] [BloonsTD6_Mod_Helper] Registering ModContent for Ultimate Crosspathing...
[20:35:28.790] [Ultimate_Crosspathing] Finished loading DartMonkeys!
[20:35:28.845] [Ultimate_Crosspathing] Finished loading BoomerangMonkeys!
[20:35:28.900] [Ultimate_Crosspathing] Finished loading BombShooters!
[20:35:28.923] [Ultimate_Crosspathing] Finished loading TackShooters!
[20:35:28.977] [Ultimate_Crosspathing] Finished loading IceMonkeys!
[20:35:29.032] [Ultimate_Crosspathing] Finished loading GlueGunners!
[20:35:29.080] [Ultimate_Crosspathing] Finished loading Desperados!
[20:35:29.192] [Ultimate_Crosspathing] Finished loading SniperMonkeys!
[20:35:29.257] [Ultimate_Crosspathing] Finished loading MonkeySubs!
[20:35:29.322] [Ultimate_Crosspathing] Finished loading MonkeyBuccaneers!
[20:35:29.377] [Ultimate_Crosspathing] Finished loading MonkeyAces!
[20:35:29.432] [Ultimate_Crosspathing] Finished loading HeliPilots!
[20:35:29.463] [Ultimate_Crosspathing] Finished loading MortarMonkeys!
[20:35:29.518] [Ultimate_Crosspathing] Finished loading DartlingGunners!
[20:35:29.568] [Ultimate_Crosspathing] Finished loading WizardMonkeys!
[20:35:29.670] [Ultimate_Crosspathing] Finished loading SuperMonkeys!
[20:35:29.701] [Ultimate_Crosspathing] Finished loading NinjaMonkeys!
[20:35:29.763] [Ultimate_Crosspathing] Finished loading Alchemists!
[20:35:29.820] [Ultimate_Crosspathing] Finished loading Druids!
[20:35:29.918] [Ultimate_Crosspathing] Finished loading Mermonkeys!
[20:35:29.962] [Ultimate_Crosspathing] Finished loading BananaFarms!
[20:35:30.031] [Ultimate_Crosspathing] Finished loading SpikeFactorys!
[20:35:30.091] [Ultimate_Crosspathing] Finished loading MonkeyVillages!
[20:35:30.141] [Ultimate_Crosspathing] Finished loading EngineerMonkeys!
[20:35:30.195] [Ultimate_Crosspathing] Finished loading BeastHandlers!
[20:35:30.208] [Ultimate_Crosspathing] Finished loading BananaFarmerPros!
[20:35:30.228] [Ultimate_Crosspathing] Finished loading SuperMonkeyBeacons!
[20:35:30.229] [BloonsTD6_Mod_Helper] Registering ModContent for EditPlayerData...
[20:35:30.229] [BloonsTD6_Mod_Helper] Registering ModContent for Industrial Farmer...
[20:35:30.287] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[20:35:30.543] [BloonsTD6_Mod_Helper] Registering ModContent for Powers in Shop...
[20:35:30.794] [BloonsTD6_Mod_Helper] Registering ModContent for Unlimited 5th Tiers +...
