
[07:45:23.719] ------------------------------
[07:45:23.720] MelonLoader v0.7.1 Open-Beta
[07:45:23.721] OS: Windows 11
[07:45:23.721] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[07:45:23.722] ------------------------------
[07:45:23.722] Game Type: Il2cpp
[07:45:23.722] Game Arch: x64
[07:45:23.722] ------------------------------
[07:45:23.723] Command-Line: 
[07:45:23.723] ------------------------------
[07:45:23.723] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[07:45:23.723] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[07:45:23.724] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[07:45:23.724] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[07:45:23.724] Runtime Type: net6
[07:45:23.786] ------------------------------
[07:45:23.786] Game Name: BloonsTD6
[07:45:23.787] Game Developer: Ninja Kiwi
[07:45:23.788] Unity Version: 6000.0.52f1
[07:45:23.788] Game Version: 50.1
[07:45:23.788] ------------------------------

[07:45:24.166] Preferences Loaded!

[07:45:24.177] Loading UserLibs...
[07:45:24.179] 0 UserLibs loaded.

[07:45:24.180] Loading Plugins...
[07:45:24.183] 0 Plugins loaded.

[07:45:24.577] Loading Il2CppAssemblyGenerator...
[07:45:24.613] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[07:45:24.785] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[07:45:24.785] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[07:45:24.785] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[07:45:24.786] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[07:45:24.792] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[07:45:24.792] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[07:45:24.792] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[07:45:24.792] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[07:45:24.792] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[07:45:24.793] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[07:45:24.793] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[07:45:24.793] [Il2CppAssemblyGenerator] Checking GameAssembly...
[07:45:24.961] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[07:45:24.962] Loading Mods...
[07:45:25.001] ------------------------------
[07:45:25.040] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[07:45:25.041] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[07:45:25.042] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[07:45:25.043] SHA256 Hash: 'C8FBC37D465D036DB0B4FB34F566E8DB6E5DDE97646C08671DCE66C19F00B8DB'

[07:45:25.585] ------------------------------
[07:45:25.585] BloonsTD6 Mod Helper v3.4.12
[07:45:25.585] by Gurrenm4 and Doombubbles
[07:45:25.586] Assembly: Btd6ModHelper.dll
[07:45:25.586] ------------------------------
[07:45:25.588] ------------------------------
[07:45:25.588] Lightning Monkey Mod v1.0.0
[07:45:25.588] by You
[07:45:25.588] Assembly: LightningMonkey.dll
[07:45:25.588] ------------------------------
[07:45:25.588] ------------------------------
[07:45:25.588] 2 Mods loaded.

[07:45:26.594] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[07:45:26.758] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[07:45:26.780] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[07:45:26.784] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[07:45:26.796] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[07:45:26.797] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[07:45:27.197] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[07:45:27.656] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[07:45:27.959] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[07:45:35.716] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 382 mods over 8.0 seconds
[07:45:37.028] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[07:45:37.030] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[07:46:45.813] Preferences Saved!
