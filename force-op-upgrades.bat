@echo off
setlocal ENABLEDELAYEDEXPANSION

echo Rewriting upgrades to OVERPOWERED values and adding icons (no PowerShell tricks)...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"
set "RES_DIR=%PROJ_DIR%\Resources"

if not exist "%UPGRADES_DIR%" mkdir "%UPGRADES_DIR%"
if not exist "%RES_DIR%" mkdir "%RES_DIR%"

REM 1) Write AllUpgrades.cs fresh with OP numbers and icon properties
(
echo using BTD_Mod_Helper.Api.Towers;
	echo using BTD_Mod_Helper.Extensions;
	echo using Il2CppAssets.Scripts.Models.Towers;
	echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
	echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
	echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
	echo using Il2CppAssets.Scripts.Unity;
	echo namespace LightningMonkey.Towers.Upgrades {
		echo   // TOP PATH (Dark Magic)
		echo   public class DarkBoost : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 1; public override int Cost =^> 250; public override string DisplayName =^> "Dark Boost"; public override string Description =^> "+1 damage, +2 pierce."; public override string Icon =^> "DarkBoost_Icon"; public override void ApplyUpgrade^(TowerModel t^) { var p=t.GetAttackModel^(^).weapons[0].projectile; p.GetDamageModel^(^).damage+=1; p.pierce+=2; } }
		echo   public class ShadowStrike : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 2; public override int Cost =^> 500; public override string DisplayName =^> "Shadow Strike"; public override string Description =^> "15%% faster."; public override string Icon =^> "ShadowStrike_Icon"; public override void ApplyUpgrade^(TowerModel t^) { t.GetAttackModel^(^).weapons[0].Rate *= 0.85f; } }
		echo   public class VoidChanneling : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 3; public override int Cost =^> 1200; public override string DisplayName =^> "Void Channeling"; public override string Description =^> "+2 dmg, +3 pierce."; public override string Icon =^> "VoidChanneling_Icon"; public override void ApplyUpgrade^(TowerModel t^) { var p=t.GetAttackModel^(^).weapons[0].projectile; p.GetDamageModel^(^).damage+=2; p.pierce+=3; } }
		echo   public class DarkMastery : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 4; public override int Cost =^> 9000; public override string DisplayName =^> "Dark Mastery"; public override string Description =^> "Bigger bolts and faster cast."; public override string Icon =^> "DarkMastery_Icon"; public override void ApplyUpgrade^(TowerModel t^) { var a=t.GetAttackModel^(^); a.weapons[0].Rate *= 0.75f; a.weapons[0].projectile.scale *= 1.25f; } }
		echo   public class VoidStormMaster : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 5; public override int Cost =^> 45000; public override string DisplayName =^> "Void Storm Master"; public override string Description =^> "Spawns a stationary black hole vortex that devours bloons."; public override string Icon =^> "VoidStormMaster_Icon"; public override void ApplyUpgrade^(TowerModel tower^) { var attack = tower^.GetAttackModel^(^); var proj = attack.weapons[0].projectile; var druid005 = Game.instance.model.GetTowerFromId^("Druid-005"^); var tornado = druid005.GetAttackModel^(^).weapons[0].projectile.Duplicate^(^); var travel = tornado.GetBehavior^<TravelStraitModel^>^(^); if ^(travel != null^) travel.speed = 0f; var age = tornado.GetBehavior^<AgeModel^>^(^); if ^(age != null^) age.Lifespan = 10f; tornado.pierce = 999999f; tornado.GetDamageModel^(^).damage = 500f; tornado.scale *= 3.0f; proj.AddBehavior^(new CreateProjectileOnContactModel^("BlackHole", tornado, new SingleEmissionModel^("", null^), true, false, false^)^); proj.GetDamageModel^(^).damage += 200f; proj.pierce += 9999f; attack.weapons[0].Rate *= 0.2f; } }
		echo   // MIDDLE PATH (Speed/Chain)
		echo   public class QuickCast : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> MIDDLE; public override int Tier =^> 1; public override int Cost =^> 150; public override string DisplayName =^> "Quick Cast"; public override string Description =^> "15%% faster"; public override string Icon =^> "QuickCast_Icon"; public override void ApplyUpgrade^(TowerModel t^) { t.GetAttackModel^(^).weapons[0].Rate *= 0.85f; } }
		echo   public class SurgeCasting : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> MIDDLE; public override int Tier =^> 2; public override int Cost =^> 300; public override string DisplayName =^> "Surge Casting"; public override string Description =^> "20%% faster"; public override string Icon =^> "SurgeCasting_Icon"; public override void ApplyUpgrade^(TowerModel t^) { t.GetAttackModel^(^).weapons[0].Rate *= 0.8f; } }
		echo   public class ChainLightning : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> MIDDLE; public override int Tier =^> 3; public override int Cost =^> 1500; public override string DisplayName =^> "Chain Lightning"; public override string Description =^> "Bolts arc heavily between many bloons."; public override string Icon =^> "ChainLightning_Icon"; public override void ApplyUpgrade^(TowerModel towerModel^) { var proj = towerModel.GetAttackModel^(^).weapons[0].projectile; var druid200 = Game.instance.model.GetTowerFromId^("Druid-200"^); var srcLm = druid200.GetAttackModel^(^).weapons[0].projectile.GetBehavior^<LightningModel^>^(^).Duplicate^(^); srcLm.splits = 12; srcLm.splitRange += 18f; var existing = proj.GetBehavior^<LightningModel^>^(^); if ^(existing != null^) proj.RemoveBehavior^<LightningModel^>^(^); proj.AddBehavior^(srcLm^); proj.pierce += 6; proj.GetDamageModel^(^).damage += 4; } }
		echo   // BOTTOM PATH (Range/Precision)
		echo   public class ExtendedRange : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> BOTTOM; public override int Tier =^> 1; public override int Cost =^> 180; public override string DisplayName =^> "Extended Range"; public override string Description =^> "+10 range"; public override string Icon =^> "ExtendedRange_Icon"; public override void ApplyUpgrade^(TowerModel t^) { t.range += 10f; } }
		echo   public class PrecisionStrike : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> BOTTOM; public override int Tier =^> 2; public override int Cost =^> 350; public override string DisplayName =^> "Precision Strike"; public override string Description =^> "+1 dmg, +8 range"; public override string Icon =^> "PrecisionStrike_Icon"; public override void ApplyUpgrade^(TowerModel t^) { t.range += 8f; t.GetAttackModel^(^).weapons[0].projectile.GetDamageModel^(^).damage += 1; } }
		echo }
) > "%UPGRADES_DIR%\AllUpgrades.cs"

REM 2) Create minimal 1x1 placeholder icons if missing
for %%I in (DarkBoost_Icon ShadowStrike_Icon VoidChanneling_Icon DarkMastery_Icon VoidStormMaster_Icon ChainLightning_Icon QuickCast_Icon SurgeCasting_Icon ExtendedRange_Icon PrecisionStrike_Icon) do (
  if not exist "%RES_DIR%\%%I.png" (
    > "%RES_DIR%\%%I.b64" echo iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9Z6E3OIAAAAASUVORK5CYII=
    certutil -f -decode "%RES_DIR%\%%I.b64" "%RES_DIR%\%%I.png" >nul 2>&1
    del /q "%RES_DIR%\%%I.b64" >nul 2>&1
  )
)

REM 3) Reminder to set csproj if duplicate resource error occurs
echo If you see NETSDK1022 duplicate EmbeddedResource, open the csproj and add:
echo   ^<PropertyGroup^>
echo     ^<EnableDefaultEmbeddedResourceItems^>false^</EnableDefaultEmbeddedResourceItems^>
echo   ^</PropertyGroup^>

echo Building OP Lightning Monkey...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

if %ERRORLEVEL% EQU 0 (
  echo ========================================
  echo SUCCESS! OP upgrades and icons applied.
  echo ========================================
) else (
  echo Build failed. If duplicate resource error persists, edit LightningMonkey.csproj as above and rebuild.
)

endlocal
pause
