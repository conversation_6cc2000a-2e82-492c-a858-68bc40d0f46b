@echo off
echo Adding missing Extensions using statements...

set "UPGRADES_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\Upgrades"

echo Fixing all upgrade files to include Extensions...
powershell -NoProfile -Command "Get-ChildItem '%UPGRADES_DIR%\*.cs' | ForEach-Object { $content = Get-Content $_.FullName -Raw; if ($content -notmatch 'BTD_Mod_Helper.Extensions') { $content = $content -replace 'using BTD_Mod_Helper\.Api\.Towers;', 'using BTD_Mod_Helper.Api.Towers; using BTD_Mod_Helper.Extensions;' }; $content | Set-Content $_.FullName }"

echo Building final version...
cd /d "%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS! Lightning Monkey mod built and copied to Mods folder!
    echo Launch BTD6 to see your new Lightning Monkey in the Magic category.
    echo.
    echo The mod includes:
    echo - Lightning Monkey tower (Magic, 550 cost)
    echo - 15 upgrades across 3 paths (5/5/5)
    echo - Top path: Damage and pierce focused
    echo - Middle path: Attack speed and multi-shot
    echo - Bottom path: Range and precision
) else (
    echo Build failed. Check errors above.
)
pause
