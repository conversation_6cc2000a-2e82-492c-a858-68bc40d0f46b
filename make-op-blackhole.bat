@echo off
setlocal ENABLEDELAYEDEXPANSION

echo Powering up Lightning Monkey to OVERPOWERED mode (huge pull, huge damage)...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "SCRIPT=lightning-staff-complete.bat"

REM 1) Buff Chain Lightning splits and range
powershell -Command "(Get-Content -Raw '%SCRIPT%') -replace 'lm\.splits \+= 3;', 'lm.splits += 12;' -replace 'lm\.splitRange \+= 5f;', 'lm.splitRange += 18f;' | Set-Content '%SCRIPT%'"

REM 2) Make the T5 black hole insanely strong and frequent
powershell -Command "^$
$p = '" + $env:SCRIPT + "';
$t = Get-Content -Raw $p;
# More frequent attacks at T5
$t = $t -replace 'attack\.weapons\[0\]\.Rate \*= 0\.6f;', 'attack.weapons[0].Rate *= 0.2f;';
# Stronger main bolt
$t = $t -replace 'proj\.GetDamageModel\(\)\.damage \+= 10f; proj\.pierce \+= 10f;', 'proj.GetDamageModel().damage += 200f; proj.pierce += 9999f;';
# Stationary black hole, huge pierce and damage
$t = $t -replace 'tornado\.pierce = 999f;', 'tornado.pierce = 999999f;';
$t = $t -replace 'tornado\.GetDamageModel\(\)\.damage = 1f;', 'tornado.GetDamageModel().damage = 500f;';
# Increase scale again
$t = $t -replace 'tornado\.scale \*= 2\.2f;', 'tornado.scale *= 3.0f;';
Set-Content -Path $p -Value $t;"

REM 3) Build
cmd /c lightning-staff-complete.bat

endlocal

