$ErrorActionPreference = 'Stop'
$p = Join-Path $env:USERPROFILE 'Documents/BTD6 Mod Sources/LightningMonkey/LightningMonkey.csproj'
if (-not (Test-Path $p)) { Write-Host "CSProj not found: $p"; exit 1 }
$content = Get-Content -Raw $p
if ($content -notmatch '<EnableDefaultEmbeddedResourceItems>') {
  $updated = [System.Text.RegularExpressions.Regex]::Replace($content,'(<Project[^>]*>)','$1`r`n  <PropertyGroup>`r`n    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>`r`n  </PropertyGroup>')
  Set-Content -Path $p -Value $updated -Encoding UTF8
  Write-Host 'Added EnableDefaultEmbeddedResourceItems=false to csproj'
} else {
  Write-Host 'EnableDefaultEmbeddedResourceItems already present'
}

