@echo off
setlocal EnableExtensions
cd /d "%~dp0"

echo ================= Setup BTD6 Mod Helper + Install Lightning Monkey =================

REM 1) Ensure Mods folder exists
if not exist "Mods" mkdir "Mods"

REM 2) Copy LightningMonkey.dll into Mods if we can find it
set "DLL=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\bin\Release\net6.0\LightningMonkey.dll"
if exist "%DLL%" (
  copy /y "%DLL%" "Mods\" >nul && echo Copied LightningMonkey.dll to Mods
) else (
  echo WARNING: Could not find built LightningMonkey.dll at:
  echo   %DLL%
  echo Build the mod first, then re-run this script.
)

REM 3) Check for Mod Helper in Mods
set "helperFound="
for /f "delims=" %%F in ('dir /b "Mods\BloonsTD6*Helper*.dll" 2^>nul') do set helperFound=1
if not defined helperFound (
  echo.
  echo MISSING: BTD6 Mod Helper .dll in Mods
  echo You MUST download and place exactly one Mod Helper DLL into Mods.
  echo Download link (latest release):
  echo   https://github.com/gurrenm3/BTD-Mod-Helper/releases
)

REM 4) Check MelonLoader version in Latest.log
set "LOG=MelonLoader\Latest.log"
if exist "%LOG%" (
  for /f "tokens=1,* delims=]" %%A in ('findstr /C:"MelonLoader v" "%LOG%"') do set "mlver=%%B"
  echo Detected%mlver%
  echo (If this shows v0.7.x Open-Beta, please install MelonLoader 0.6.1 stable.)
) else (
  echo Launch the game once to generate MelonLoader\Latest.log so we can check version.
)

REM 5) Optional clean caches
choice /c YN /n /m "Clear MelonLoader caches now? [Y/N]: "
if errorlevel 2 goto :SKIP
for %%D in ("MelonLoader\Dependencies" "MelonLoader\Managed" "UserData\BTD6ModHelper") do (
  if exist %%D (
    echo Deleting %%D ...
    rmdir /s /q %%D
  )
)
:SKIP

echo.
echo Next steps:
echo   - Ensure Mods contains exactly ONE 'BloonsTD6 Mod Helper*.dll' (latest)
echo   - Use MelonLoader 0.6.1 (stable), not 0.7.0 Open-Beta

echo Done. Launch the game and the Mod Menu should appear.
endlocal
pause
