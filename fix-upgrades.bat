@echo off
echo Fixing upgrade namespace issues...

set "UPGRADES_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\Upgrades"

echo Fixing all upgrade files...
powershell -NoProfile -Command "Get-ChildItem '%UPGRADES_DIR%\*.cs' | ForEach-Object { (Get-Content $_.FullName -Raw) -replace 'using BTD_Mod_Helper\.Api\.Upgrades;','using BTD_Mod_Helper.Api.Towers;' | Set-Content $_.FullName }"

echo Fixing tower file...
set "TOWER_FILE=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\LightningMonkey.cs"
powershell -NoProfile -Command "(Get-Content '%TOWER_FILE%' -Raw) -replace 'TowerSet\.Magic','TowerSet.Magic' | Set-Content '%TOWER_FILE%'"

echo Building again...
cd /d "%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
dotnet build -c Release --nologo

echo.
echo Build complete! Check above for success.
pause
