$ErrorActionPreference = 'Stop'
$path = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey\Towers\LightningMonkey.cs'
$lines = @(
'using BTD_Mod_Helper.Api.Towers;',
'using BTD_Mod_Helper.Extensions;',
'using Il2CppAssets.Scripts.Models.Towers;',
'using Il2CppAssets.Scripts.Models.TowerSets;',
'using Il2CppAssets.Scripts.Models.Towers.Weapons;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;',
'using Il2CppAssets.Scripts.Unity;',
'',
'namespace LightningMonkey.Towers',
'{',
'    public class LightningMonkeyTower : ModTower',
'    {',
'        public override string BaseTower => TowerType.Druid;',
'        public override TowerSet TowerSet => TowerSet.Magic;',
'',
'        public override string DisplayName => "Lightning Monkey";',
'        public override string Description => "Harnesses crackling lightning that chains through bloons while conjuring void vortices.";',
'        public override int Cost => 650;',
'',
'        public override int TopPathUpgrades => 5;',
'        public override int MiddlePathUpgrades => 3;',
'        public override int BottomPathUpgrades => 2;',
'',
'        public override void ModifyBaseTowerModel(TowerModel towerModel)',
'        {',
'            towerModel.range += 12f;',
'',
'            var attack = towerModel.GetAttackModel();',
'            if (attack == null) return;',
'',
'            var baseWeapon = attack.weapons[0];',
'            baseWeapon.Rate = 1.0f;',
'',
'            // Lightning projectile from Druid-200 (ensures LightningModel visuals/behavior)',
'            try',
'            {',
'                var druid200 = Game.instance.model.GetTowerFromId("Druid-200");',
'                if (druid200 != null)',
'                {',
'                    var druidAttack = druid200.GetAttackModel();',
'                    ProjectileModel source = null;',
'                    foreach (var w in druidAttack.weapons)',
'                    {',
'                        if (w.projectile.GetBehavior<LightningModel>() != null)',
'                        {',
'                            source = w.projectile;',
'                            break;',
'                        }',
'                    }',
'',
'                    if (source != null)',
'                    {',
'                        var lightning = source.Duplicate();',
'                        lightning.pierce = 4f;',
'                        lightning.GetDamageModel().damage = 2f;',
'                        lightning.scale = 1.15f;',
'                        baseWeapon.projectile = lightning;',
'                    }',
'                }',
'            }',
'            catch { }',
'',
'            // Add a second weapon that fires a vortex/tornado as a "black hole" analogue (from Druid-005 Superstorm)',
'            try',
'            {',
'                var druid005 = Game.instance.model.GetTowerFromId("Druid-005");',
'                if (druid005 != null)',
'                {',
'                    var tornadoProj = druid005.GetAttackModel().weapons[0].projectile.Duplicate();',
'                    // Darken/scale and adjust stats to complement lightning',
'                    tornadoProj.pierce = 40f;',
'                    var dmg = tornadoProj.GetDamageModel();',
'                    if (dmg != null) dmg.damage = 1f;',
'                    tornadoProj.scale *= 1.5f;',
'',
'                    var blackHoleWeapon = baseWeapon.Duplicate();',
'                    blackHoleWeapon.name = "BlackHoleWeapon";',
'                    blackHoleWeapon.projectile = tornadoProj;',
'                    blackHoleWeapon.Rate = 1.25f;',
'                    attack.AddWeapon(blackHoleWeapon);',
'                }',
'            }',
'            catch { }',
'        }',
'    }',
'}'
)
$lines | Set-Content -Encoding UTF8 -LiteralPath $path
Write-Host 'Overwrote LightningMonkey.cs with dual-weapon (lightning + black hole) implementation.'

