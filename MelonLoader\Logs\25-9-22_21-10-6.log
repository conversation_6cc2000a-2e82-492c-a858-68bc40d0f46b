
[21:10:07.382] ------------------------------
[21:10:07.384] MelonLoader v0.7.1 Open-Beta
[21:10:07.385] OS: Windows 11
[21:10:07.385] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[21:10:07.386] ------------------------------
[21:10:07.386] Game Type: Il2cpp
[21:10:07.387] Game Arch: x64
[21:10:07.387] ------------------------------
[21:10:07.387] Command-Line: 
[21:10:07.387] ------------------------------
[21:10:07.387] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:10:07.388] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:10:07.388] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[21:10:07.388] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[21:10:07.388] Runtime Type: net6
[21:10:07.455] ------------------------------
[21:10:07.455] Game Name: BloonsTD6
[21:10:07.456] Game Developer: Ninja Kiwi
[21:10:07.457] Unity Version: 6000.0.58f1
[21:10:07.458] Game Version: UNKNOWN
[21:10:07.458] ------------------------------

[21:10:07.852] Preferences Loaded!

[21:10:07.864] Loading UserLibs...
[21:10:07.866] 0 UserLibs loaded.

[21:10:07.866] Loading Plugins...
[21:10:07.870] 0 Plugins loaded.

[21:10:08.253] Loading Il2CppAssemblyGenerator...
[21:10:08.290] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[21:10:08.551] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[21:10:08.551] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[21:10:08.552] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[21:10:08.552] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[21:10:08.558] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[21:10:08.558] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[21:10:08.558] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.58
[21:10:08.558] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[21:10:08.558] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[21:10:08.559] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[21:10:08.559] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[21:10:08.559] [Il2CppAssemblyGenerator] Checking GameAssembly...
[21:10:08.697] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[21:10:08.698] Loading Mods...
[21:10:08.734] ------------------------------
[21:10:08.771] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[21:10:08.772] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[21:10:08.774] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[21:10:08.774] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[21:10:08.778] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[21:10:08.778] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[21:10:09.328] ------------------------------
[21:10:09.328] BloonsTD6 Mod Helper v3.4.12
[21:10:09.328] by Gurrenm4 and Doombubbles
[21:10:09.329] Assembly: Btd6ModHelper.dll
[21:10:09.329] ------------------------------
[21:10:09.332] ------------------------------
[21:10:09.333] Faster Forward v1.1.5
[21:10:09.333] by doombubbles
[21:10:09.333] Assembly: FasterForward.dll
[21:10:09.333] ------------------------------
[21:10:09.336] ------------------------------
[21:10:09.336] Unlimited 5th Tiers + v1.1.9
[21:10:09.336] by doombubbles
[21:10:09.336] Assembly: Unlimited5thTiers.dll
[21:10:09.336] ------------------------------
[21:10:09.336] ------------------------------
[21:10:09.336] 3 Mods loaded.

[21:10:10.800] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[21:10:10.974] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
