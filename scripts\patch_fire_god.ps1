$ErrorActionPreference = 'Stop'
$root = 'C:\\Users\\<USER>\\Documents\\BTD6 Mod Sources\\LightningMonkey'
$lt = Join-Path $root 'Towers\\LightningMonkey.cs'
$csproj = Join-Path $root 'LightningMonkey.csproj'
$resDir = Join-Path $root 'Resources'
$upg = Join-Path $root 'Towers\\Upgrades\\AllUpgrades.cs'

if (-not (Test-Path -LiteralPath $lt)) { throw "Missing file: $lt" }
if (-not (Test-Path -LiteralPath $csproj)) { throw "Missing file: $csproj" }
if (-not (Test-Path -LiteralPath $upg)) { throw "Missing file: $upg" }
if (-not (Test-Path -LiteralPath $resDir)) { New-Item -ItemType Directory -Path $resDir | Out-Null }

# Write small placeholder PNGs using base64 (replace later with your art)
$iconPath = Join-Path $resDir 'LightningMonkey-Icon.png'
$portraitPath = Join-Path $resDir 'LightningMonkey-Portrait.png'
$pngBase64_1x1_blue = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGP4////fwAJ+wP9AAAAAElFTkSuQmCC'
$pngBase64_1x1_orange = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGP4//8/AwMDgAEAAZ6fBVoAAAAASUVORK5CYII='
[IO.File]::WriteAllBytes($iconPath, [Convert]::FromBase64String($pngBase64_1x1_blue))
[IO.File]::WriteAllBytes($portraitPath, [Convert]::FromBase64String($pngBase64_1x1_orange))

# Ensure Icon/Portrait overrides exist in ModTower
$src = Get-Content -Raw -LiteralPath $lt
if ($src -notmatch 'public\s+override\s+string\s+Icon') {
  $src = $src -replace "(public\s+override\s+int\s+Cost\s*=>\s*\d+;)", "$1`n`n        public override string Icon => \"LightningMonkey-Icon\";`n        public override string Portrait => \"LightningMonkey-Portrait\";"
}
Set-Content -LiteralPath $lt -Encoding UTF8 -Value $src

# Ensure Resources are embedded
[xml]$doc = Get-Content -Raw -LiteralPath $csproj
$found = $false
foreach ($ig in $doc.Project.ItemGroup) {
  foreach ($n in $ig.ChildNodes) { if ($n.Name -eq 'EmbeddedResource' -and $n.Include -like 'Resources*') { $found = $true } }
}
if (-not $found) {
  $ig = $doc.CreateElement('ItemGroup')
  $er = $doc.CreateElement('EmbeddedResource')
  $er.SetAttribute('Include','Resources\\**\\*.png')
  [void]$ig.AppendChild($er)
  [void]$doc.Project.AppendChild($ig)
  $doc.Save($csproj)
}

# Append Fire God upgrade if not already present
$upgSrc = Get-Content -Raw -LiteralPath $upg
if ($upgSrc -notmatch 'class\s+FireGod') {
  $append = @'

// Fire God transformation (T5 Top)
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;

namespace LightningMonkey.Towers.Upgrades {
  public class FireGod : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => TOP; public override int Tier => 5; public override int Cost => 65000;
    public override string DisplayName => "Fire God";
    public override string Description => "Transforms into a blazing deity with a burning aura.";
    public override void ApplyUpgrade(TowerModel t) {
      // Visual: imposing fire-themed look
      var wiz520 = Game.instance.model.GetTowerFromId("WizardMonkey-520"); if (wiz520 != null) t.display = wiz520.display;
      t.displayScale *= 1.15f;

      // Burn aura: borrow Ring of Fire attack
      var tack400 = Game.instance.model.GetTowerFromId("TackShooter-400");
      if (tack400 != null) {
        var aura = tack400.GetAttackModel().Duplicate();
        aura.name = "FireGodAura";
        aura.range = t.range; // use current tower range
        var w = aura.weapons[0];
        w.Rate = 0.25f; // frequent ticks
        var proj = w.projectile;
        var dot = proj.GetBehavior<DamageOverTimeModel>();
        if (dot != null) {
          dot.damage = 3f;    // 3 dmg per tick
          dot.interval = 1f;  // per second
          dot.lifespan = 4f;  // lasts 4 seconds
        }
        t.AddBehavior(aura);
      }
    }
  }
}
'@
  Add-Content -LiteralPath $upg -Value $append
}

Write-Host 'Patched: custom assets + Fire God upgrade.'

