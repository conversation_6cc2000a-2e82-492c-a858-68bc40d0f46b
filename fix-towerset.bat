@echo off
echo Fixing TowerSet issue...

set "TOWER_FILE=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\LightningMonkey.cs"

echo Adding missing using statement...
powershell -NoProfile -Command "$content = Get-Content '%TOWER_FILE%' -Raw; if ($content -notmatch 'using Il2CppAssets.Scripts.Models.Towers.Mods') { $content = $content -replace 'using Il2CppAssets.Scripts.Models.Towers;', 'using Il2CppAssets.Scripts.Models.Towers;`nusing Il2CppAssets.Scripts.Models.TowerSets;' }; $content | Set-Content '%TOWER_FILE%'"

echo Building final version...
cd /d "%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS! Lightning Monkey mod built and copied to Mods folder!
    echo Launch BTD6 to see your new Lightning Monkey in the Magic category.
) else (
    echo Build failed. Check errors above.
)
pause
