using MelonLoader;
using BTD_Mod_Helper;
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Attack;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Abilities;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Models.Towers.Weapons;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
using Il2CppAssets.Scripts.Unity;
using BTD_Mod_Helper.Api.Enums;

[assembly: MelonInfo(typeof(BlazeHero.Main), "Blaze Hero", "1.0.0", "YourName")]
[assembly: <PERSON><PERSON><PERSON><PERSON>("Ninja Kiwi", "BloonsTD6")]

namespace BlazeHero
{
    public class Main : BloonsTD6Mod
    {
        public override void OnApplicationStart()
        {
            MelonLogger.Msg("Blaze Hero Loaded! 🔥");
        }
    }

    public class Blaze : ModHero
    {
        public override string BaseTower => TowerType.Quincy;
        public override int Cost => 750;
        public override string Description => "<PERSON> is a mobile flamethrowing hero who chases balloons around the map with devastating blue fire attacks!";
        public override string DisplayName => "Blaze";
        public override string Name => "Blaze";
        public override string Title => "The Flame Chaser";
        public override string Level1Description => "A fearless monkey with a flamethrower who never lets a balloon escape!";

        public override void ModifyBaseTowerModel(TowerModel towerModel)
        {
            // Base stats - make him a flamethrower
            towerModel.range = 50f;
            towerModel.cost = 750f;

            // Get the attack model and modify it
            var attackModel = towerModel.GetAttackModel();
            attackModel.range = 50f;

            // Modify weapon to be like a flamethrower
            var weaponModel = attackModel.weapons[0];
            weaponModel.rate = 0.1f; // Very fast attack rate

            // Create flame projectile based on Wizard's fireball
            var wizardFireball = Game.instance.model.GetTowerFromId("WizardMonkey-010").GetAttackModel().weapons[0].projectile.Duplicate();
            weaponModel.projectile = wizardFireball;

            // Enhance the projectile for Blaze
            var projectileModel = weaponModel.projectile;
            projectileModel.id = "BlazeFlameProjectile";
            projectileModel.maxPierce = 10f;
            projectileModel.pierce = 10f;
            projectileModel.radius = 12f;

            // Increase damage
            var damageModel = projectileModel.GetDamageModel();
            damageModel.damage = 3f;

            // Add burning effect
            if (!projectileModel.HasBehavior<DamageOverTimeModel>())
            {
                var burnEffect = new DamageOverTimeModel("BlazeBurn", 2f, 4f, false, false, false, false, false);
                projectileModel.AddBehavior(burnEffect);
            }

            // Make projectiles seek targets (simulates chasing)
            var seekingBehavior = new TrackTargetModel("BlazeHoming", 999f, true, false, 180f, false, 999f, false, false);
            projectileModel.AddBehavior(seekingBehavior);
        }

        public override int MaxLevel => 20;

        // Simplified level progression focused on chasing and flames
        public override void ModifyTowerModelForLevel(TowerModel towerModel, int level)
        {
            var attackModel = towerModel.GetAttackModel();
            var weaponModel = attackModel.weapons[0];
            var projectileModel = weaponModel.projectile;
            var damageModel = projectileModel.GetDamageModel();

            // Progressive improvements
            switch (level)
            {
                case 2:
                    damageModel.damage += 1f;
                    towerModel.range += 10f;
                    attackModel.range += 10f;
                    break;

                case 3:
                    // Faster attack rate (more flames!)
                    weaponModel.rate *= 0.8f;
                    break;

                case 4:
                    // Can pop lead balloons with hot flames
                    damageModel.immuneBloonProperties = Il2Cpp.BloonProperties.None;
                    break;

                case 5:
                    projectileModel.pierce += 3f;
                    projectileModel.maxPierce += 3f;
                    break;

                case 6:
                    // Better seeking (chases better)
                    var seeking = projectileModel.GetBehavior<TrackTargetModel>();
                    if (seeking != null)
                    {
                        seeking.distance = 999f;
                        seeking.constantlyAquireNewTarget = true;
                    }
                    break;

                case 8:
                    damageModel.damage += 3f;
                    // Increase burn damage
                    var burnEffect = projectileModel.GetBehavior<DamageOverTimeModel>();
                    if (burnEffect != null)
                    {
                        burnEffect.damage += 1f;
                    }
                    break;

                case 10:
                    // Massive range increase (can chase across map)
                    towerModel.range = 200f;
                    attackModel.range = 200f;
                    weaponModel.rate *= 0.7f; // Even faster
                    break;

                case 15:
                    // Explosive flames
                    damageModel.damage += 5f;
                    projectileModel.radius += 5f;
                    break;

                case 20:
                    // Ultimate Blaze - massive damage and range
                    damageModel.damage = 25f;
                    towerModel.range = 999f;
                    attackModel.range = 999f;
                    weaponModel.rate = 0.05f; // Super fast
                    projectileModel.pierce = 50f;
                    projectileModel.maxPierce = 50f;
                    break;
            }
        }
    }

}
