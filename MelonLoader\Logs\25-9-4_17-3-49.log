
[17:03:49.410] ------------------------------
[17:03:49.412] MelonLoader v0.7.1 Open-Beta
[17:03:49.413] OS: Windows 11
[17:03:49.413] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[17:03:49.413] ------------------------------
[17:03:49.414] Game Type: Il2cpp
[17:03:49.414] Game Arch: x64
[17:03:49.414] ------------------------------
[17:03:49.414] Command-Line: 
[17:03:49.414] ------------------------------
[17:03:49.414] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:03:49.415] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:03:49.415] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[17:03:49.415] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[17:03:49.415] Runtime Type: net6
[17:03:49.479] ------------------------------
[17:03:49.479] Game Name: BloonsTD6
[17:03:49.479] Game Developer: Ninja Kiwi
[17:03:49.481] Unity Version: 6000.0.52f1
[17:03:49.481] Game Version: 50.1
[17:03:49.481] ------------------------------

[17:03:49.884] Preferences Loaded!

[17:03:49.896] Loading UserLibs...
[17:03:49.899] 0 UserLibs loaded.

[17:03:49.899] Loading Plugins...
[17:03:49.903] 0 Plugins loaded.

[17:03:50.600] Loading Il2CppAssemblyGenerator...
[17:03:50.637] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[17:03:50.836] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[17:03:50.837] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[17:03:50.837] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[17:03:50.837] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[17:03:50.844] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[17:03:50.844] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[17:03:50.844] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[17:03:50.844] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[17:03:50.844] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[17:03:50.844] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[17:03:50.844] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[17:03:50.845] [Il2CppAssemblyGenerator] Checking GameAssembly...
[17:03:51.024] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[17:03:51.025] Loading Mods...
[17:03:51.057] ------------------------------
[17:03:51.097] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[17:03:51.097] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[17:03:51.098] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[17:03:51.099] SHA256 Hash: 'C8FBC37D465D036DB0B4FB34F566E8DB6E5DDE97646C08671DCE66C19F00B8DB'

[17:03:51.624] ------------------------------
[17:03:51.624] BloonsTD6 Mod Helper v3.4.12
[17:03:51.624] by Gurrenm4 and Doombubbles
[17:03:51.625] Assembly: Btd6ModHelper.dll
[17:03:51.625] ------------------------------
[17:03:51.627] ------------------------------
[17:03:51.627] Lightning Monkey Mod v1.0.0
[17:03:51.627] by You
[17:03:51.627] Assembly: LightningMonkey.dll
[17:03:51.627] ------------------------------
[17:03:51.628] ------------------------------
[17:03:51.628] 2 Mods loaded.

[17:03:52.606] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[17:03:52.779] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[17:03:52.802] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[17:03:52.805] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[17:03:52.818] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[17:03:52.820] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[17:03:53.231] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[17:03:53.795] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[17:03:54.155] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[17:04:02.502] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 382 mods over 8.7 seconds
[17:04:03.362] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[17:04:03.364] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
