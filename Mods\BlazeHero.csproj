<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>BlazeHero</RootNamespace>
    <AssemblyName>BlazeHero</AssemblyName>
    <LangVersion>latest</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>false</Optimize>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="BloonsTD6 Mod Helper">
      <HintPath>$(GameDir)\Mods\Btd6ModHelper.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Il2CppInterop.Runtime">
      <HintPath>$(GameDir)\MelonLoader\Il2CppInterop.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Il2CppInterop.Common">
      <HintPath>$(GameDir)\MelonLoader\Il2CppInterop.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Il2Cppmscorlib">
      <HintPath>$(GameDir)\MelonLoader\Managed\Il2Cppmscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Il2CppSystem">
      <HintPath>$(GameDir)\MelonLoader\Managed\Il2CppSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Il2CppSystem.Core">
      <HintPath>$(GameDir)\MelonLoader\Managed\Il2CppSystem.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine">
      <HintPath>$(GameDir)\MelonLoader\Managed\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>$(GameDir)\MelonLoader\Managed\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Il2CppAssets.Scripts">
      <HintPath>$(GameDir)\MelonLoader\Managed\Il2CppAssets.Scripts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MelonLoader">
      <HintPath>$(GameDir)\MelonLoader\MelonLoader.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="copy &quot;$(TargetPath)&quot; &quot;$(GameDir)\Mods\&quot;" />
  </Target>
</Project>
