$ErrorActionPreference = 'Stop'
$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$tf = Join-Path $root 'Towers\CelestialDeity.cs'
$rf = Join-Path $root 'Resources'

# Ensure target directories exist
New-Item -ItemType Directory -Force -Path (Split-Path $tf) | Out-Null
New-Item -ItemType Directory -Force -Path $rf | Out-Null

# Write the new tower source file
$code = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.TowerSets;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers {
  public class CelestialDeity : ModTower {
    public override string BaseTower => TowerType.SuperMonkey;
    public override TowerSet TowerSet => TowerSet.Magic;

    public override string DisplayName => "Celestial Deity";
    public override string Description => "A godlike entity that summons sunfire and purging flames to engulf the map.";
    public override int Cost => 15000;
    public override string Icon => "CelestialDeity-Icon";
    public override string Portrait => "CelestialDeity-Portrait";

    public override int TopPathUpgrades => 0;
    public override int MiddlePathUpgrades => 0;
    public override int BottomPathUpgrades => 0;

    public override void ModifyBaseTowerModel(TowerModel t) {
      // Massive vision
      t.range += 100f;

      // Add a copy of True Sun God's attack, heavily amped
      var tsg = Game.instance.model.GetTowerFromId("SuperMonkey-520");
      if (tsg != null) {
        var op = tsg.GetAttackModel().Duplicate();
        foreach (var w in op.weapons) {
          w.Rate *= 0.5f; // double fire rate
          var p = w.projectile;
          p.pierce = 9999f;
          var d = p.GetDamageModel(); if (d != null) d.damage *= 4f;
          p.scale *= 1.5f;
        }
        t.AddBehavior(op);
      }

      // Layer multiple Inferno Ring auras across the full range for insane AoE
      var inferno = Game.instance.model.GetTowerFromId("TackShooter-520");
      if (inferno != null) {
        for (int i = 0; i < 3; i++) {
          var aura = inferno.GetAttackModel().Duplicate();
          aura.name = "CelestialAura" + i;
          aura.range = t.range;
          var w = aura.weapons[0];
          w.Rate = 0.15f; // frequent ticks
          var p = w.projectile;
          var d = p.GetDamageModel(); if (d != null) d.damage = 35f;
          p.pierce = 9999f;
          p.scale *= 2.0f;
          t.AddBehavior(aura);
        }
      }

      // Slight size boost for presence
      t.displayScale *= 1.15f;
    }
  }
}
'@
[IO.File]::WriteAllText($tf, $code, [Text.Encoding]::UTF8)

# Copy placeholder icon/portrait if LightningMonkey ones exist
$iconSrc = Join-Path $rf 'LightningMonkey-Icon.png'
$porSrc  = Join-Path $rf 'LightningMonkey-Portrait.png'
$iconDst = Join-Path $rf 'CelestialDeity-Icon.png'
$porDst  = Join-Path $rf 'CelestialDeity-Portrait.png'
if (Test-Path $iconSrc -PathType Leaf -ErrorAction SilentlyContinue) { Copy-Item $iconSrc $iconDst -Force }
if (Test-Path $porSrc  -PathType Leaf -ErrorAction SilentlyContinue) { Copy-Item $porSrc  $porDst  -Force }

Write-Host 'CelestialDeity tower created with placeholder art (if available).'

