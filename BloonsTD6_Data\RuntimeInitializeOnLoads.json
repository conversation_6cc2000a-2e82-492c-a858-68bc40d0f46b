{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "As<PERSON><PERSON>", "className": "RuntimeInitializeOnLoad", "methodName": "OnBeforeSceneLoadRuntimeMethod", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "Assets.Scripts.Unity.Utils", "className": "AtlasLateBinding", "methodName": "sInit", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1221673671587648887", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Newtonsoft.Json.UnityConverters", "nameSpace": "Newtonsoft.Json.UnityConverters", "className": "UnityConverterInitializer", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "NinjaKiwi.Common", "nameSpace": "NinjaKiwi.Common", "className": "MonoBehaviourSingletonUtility", "methodName": "OnApplicationStart", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Unity.2D.SpriteShape.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Burst", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1652832624114795843", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem.UI", "className": "InputSystemUIInputModule", "methodName": "ResetDefaultActions", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem.ForUI", "nameSpace": "UnityEngine.InputSystem.Plugins.InputForUI", "className": "InputSystemProvider", "methodName": "Bootstrap", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Rendering.LightTransport.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__16164947281921951637", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.GPUDriven.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__15867191014387474753", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.GPUDriven.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Universal.2D.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.ResourceManager", "nameSpace": "UnityEngine.ResourceManagement.ResourceProviders", "className": "AssetBundleProvider", "methodName": "Init", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Services.CloudDiagnostics", "nameSpace": "Unity.Services.CloudDiagnostics", "className": "CloudDiagnosticsInitializer", "methodName": "Register", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core", "nameSpace": "Unity.Services.Core", "className": "UnityThreadUtils", "methodName": "CaptureUnityThreadInfo", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "TaskAsyncOperation", "methodName": "SetScheduler", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "CreateStaticInstance", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "EnableServicesInitializationAsync", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Registration", "nameSpace": "Unity.Services.Core.Registration", "className": "CorePackageInitializer", "methodName": "InitializeOnLoad", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "UnityEngine.Purchasing.Codeless", "nameSpace": "UnityEngine.Purchasing", "className": "CodelessIAPStoreListener", "methodName": "InitializeCodelessPurchasingOnLoad", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "UnityEngine.Purchasing.Stores", "nameSpace": "UnityEngine.Purchasing.Registration", "className": "IapCoreInitializeCallback", "methodName": "Register", "loadTypes": 1, "isUnityClass": true}]}