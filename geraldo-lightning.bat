@echo off
echo Creating <PERSON> Monkey based on <PERSON><PERSON> for perfect lightning effects...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

echo Writing Lightning Monkey with <PERSON><PERSON> base for magical lightning staff...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.Geraldo;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "A mystical monkey shopkeeper who has mastered the art of lightning magic! Wields a powerful staff that channels crackling electrical energy through bloons.";
echo     public override int Cost =^> 550;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 18f;
echo       var atk = m.GetAttackModel^(^);
echo       if ^(atk != null^) {
echo         atk.weapons[0].Rate = 0.7f;
echo         var p = atk.weapons[0].projectile;
echo         p.pierce += 4;
echo         p.GetDamageModel^(^).damage = 4;
echo         p.scale = 1.5f;
echo       }
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building Lightning Monkey with Geraldo base...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey with Geraldo base!
    echo ========================================
    echo.
    echo PERFECT MATCH for your image:
    echo - Based on Geraldo ^(Mystic Shopkeeper^)
    echo - Brown monkey with magical staff
    echo - Already has lightning/electrical abilities
    echo - Cyan/turquoise magical effects
    echo - Proper casting animations
    echo - Mystical appearance with staff and orb
    echo.
    echo Enhanced stats:
    echo - +18 range ^(magical staff extends reach^)
    echo - 4 damage ^(powerful lightning^)
    echo - +4 pierce ^(lightning chains through bloons^)
    echo - 1.5x larger projectiles
    echo - 0.7s attack rate ^(fast lightning casting^)
    echo.
    echo This should look much closer to your lightning monkey image!
    echo Launch BTD6 to see your Lightning Monkey!
    echo ^(Look in Magic category, cost 550^)
) else (
    echo Build failed. Check errors above.
)
pause
