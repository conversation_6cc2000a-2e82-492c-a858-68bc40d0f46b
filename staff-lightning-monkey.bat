@echo off
echo Creating <PERSON> Monkey with normal monkey + <PERSON><PERSON>'s staff...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

echo Writing Lightning Monkey with Dart Monkey base + staff weapon...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Weapons;
echo using Il2CppAssets.Scripts.Unity;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Attack;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Abilities;
echo using Il2CppAssets.Scripts.Models.GenericBehaviors;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.DartMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "A normal monkey who found a mystical lightning staff! Shoots powerful lightning bolts that chain through bloons with crackling electrical energy.";
echo     public override int Cost =^> 650;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel towerModel^) {
echo       // Enhanced range for staff weapon
echo       towerModel.range += 20f;
echo.
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var weaponModel = attackModel.weapons[0];
echo.
echo         // Lightning staff attack rate
echo         weaponModel.Rate = 0.8f;
echo.
echo         // Get Geraldo's lightning projectile as reference
echo         var geraldoModel = Game.instance.model.GetTowerFromId^(TowerType.Geraldo^);
echo         var geraldoAttack = geraldoModel.GetAttackModel^(^);
echo.
echo         if ^(geraldoAttack != null^) {
echo           // Use Geraldo's projectile as base for lightning effect
echo           var lightningProjectile = geraldoAttack.weapons[0].projectile.Duplicate^(^);
echo.
echo           // Customize lightning projectile
echo           lightningProjectile.pierce = 6f;
echo           lightningProjectile.GetDamageModel^(^).damage = 3f;
echo           lightningProjectile.scale = 1.4f;
echo.
echo           // Add lightning chain effect
echo           lightningProjectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo             "LightningChain",
echo             lightningProjectile.Duplicate^(^),
echo             new SingleEmissionModel^("", null^),
echo             true, false, false
echo           ^)^);
echo.
echo           weaponModel.projectile = lightningProjectile;
echo         }
echo.
echo         // Try to reference Geraldo's staff visual
echo         // Note: This is a simplified approach - full staff attachment
echo         // would require custom 3D model work beyond BTD6 Mod Helper
echo         var geraldoDisplay = geraldoModel.display;
echo         if ^(geraldoDisplay != null^) {
echo           // Attempt to blend displays ^(limited success expected^)
echo           towerModel.display = geraldoDisplay;
echo         }
echo       }
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building Lightning Monkey with staff weapon...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey with Staff!
    echo ========================================
    echo.
    echo FEATURES:
    echo - Normal monkey base ^(Dart Monkey foundation^)
    echo - Lightning projectiles from Geraldo's abilities
    echo - Enhanced range +20 ^(staff reach^)
    echo - 3 damage lightning bolts
    echo - 6 pierce ^(chains through bloons^)
    echo - 1.4x larger projectiles
    echo - 0.8s attack rate
    echo - Lightning chain effects
    echo.
    echo VISUAL LIMITATIONS:
    echo - BTD6 Mod Helper has limited 3D model editing
    echo - Full staff attachment requires advanced tools
    echo - This version uses Geraldo's projectile effects
    echo - For perfect visual match, you'd need:
    echo   * Unity 3D model editing
    echo   * Custom texture work
    echo   * Advanced BTD6 modding tools
    echo.
    echo Launch BTD6 to test! ^(Magic category, 650 cost^)
) else (
    echo Build failed. Check errors above.
)
pause
