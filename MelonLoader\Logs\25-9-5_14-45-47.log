
[14:45:48.186] ------------------------------
[14:45:48.188] MelonLoader v0.7.1 Open-Beta
[14:45:48.189] OS: Windows 11
[14:45:48.189] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[14:45:48.189] ------------------------------
[14:45:48.189] Game Type: Il2cpp
[14:45:48.189] Game Arch: x64
[14:45:48.189] ------------------------------
[14:45:48.190] Command-Line: 
[14:45:48.190] ------------------------------
[14:45:48.190] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[14:45:48.190] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[14:45:48.190] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[14:45:48.190] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[14:45:48.190] Runtime Type: net6
[14:45:48.251] ------------------------------
[14:45:48.251] Game Name: BloonsTD6
[14:45:48.252] Game Developer: Ninja Kiwi
[14:45:48.253] Unity Version: 6000.0.52f1
[14:45:48.253] Game Version: 50.1
[14:45:48.253] ------------------------------

[14:45:48.635] Preferences Loaded!

[14:45:48.647] Loading UserLibs...
[14:45:48.649] 0 UserLibs loaded.

[14:45:48.649] Loading Plugins...
[14:45:48.653] 0 Plugins loaded.

[14:45:49.270] Loading Il2CppAssemblyGenerator...
[14:45:49.316] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[14:45:50.050] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[14:45:50.050] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[14:45:50.051] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[14:45:50.051] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[14:45:50.060] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[14:45:50.060] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[14:45:50.060] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[14:45:50.060] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[14:45:50.060] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[14:45:50.061] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[14:45:50.061] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[14:45:50.061] [Il2CppAssemblyGenerator] Checking GameAssembly...
[14:45:50.268] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[14:45:50.270] Loading Mods...
[14:45:50.313] ------------------------------
[14:45:50.346] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[14:45:50.346] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[14:45:50.347] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[14:45:50.348] SHA256 Hash: '8FB218B4AFC5C5C0C45B55E3F0D2F4FD98A38E1B36B8289FED5BC3D23827F676'

[14:45:50.811] ------------------------------
[14:45:50.812] BloonsTD6 Mod Helper v3.4.12
[14:45:50.813] by Gurrenm4 and Doombubbles
[14:45:50.813] Assembly: Btd6ModHelper.dll
[14:45:50.813] ------------------------------
[14:45:50.815] ------------------------------
[14:45:50.815] Lightning Monkey Mod v1.0.0
[14:45:50.816] by You
[14:45:50.816] Assembly: LightningMonkey.dll
[14:45:50.816] ------------------------------
[14:45:50.817] ------------------------------
[14:45:50.817] 2 Mods loaded.

[14:45:51.787] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[14:45:51.960] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[14:45:51.984] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[14:45:51.988] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[14:45:52.001] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[14:45:52.003] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[14:45:52.411] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[14:45:52.901] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[14:45:53.241] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[14:45:59.500] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 382 mods over 6.6 seconds
[14:46:02.845] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[14:46:02.848] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[14:51:47.226] Preferences Saved!
