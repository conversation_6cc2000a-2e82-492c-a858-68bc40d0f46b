
[21:01:38.195] ------------------------------
[21:01:38.197] MelonLoader v0.7.1 Open-Beta
[21:01:38.198] OS: Windows 11
[21:01:38.198] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[21:01:38.198] ------------------------------
[21:01:38.199] Game Type: Il2cpp
[21:01:38.199] Game Arch: x64
[21:01:38.199] ------------------------------
[21:01:38.200] Command-Line: 
[21:01:38.200] ------------------------------
[21:01:38.200] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:01:38.200] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:01:38.201] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[21:01:38.201] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[21:01:38.202] Runtime Type: net6
[21:01:38.265] ------------------------------
[21:01:38.265] Game Name: BloonsTD6
[21:01:38.265] Game Developer: Ninja Kiwi
[21:01:38.267] Unity Version: 6000.0.52f1
[21:01:38.267] Game Version: 50.2
[21:01:38.267] ------------------------------

[21:01:38.671] Preferences Loaded!

[21:01:38.682] Loading UserLibs...
[21:01:38.684] 0 UserLibs loaded.

[21:01:38.685] Loading Plugins...
[21:01:38.690] 0 Plugins loaded.

[21:01:39.103] Loading Il2CppAssemblyGenerator...
[21:01:39.142] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[21:01:39.319] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[21:01:39.320] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[21:01:39.320] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[21:01:39.320] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[21:01:39.327] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[21:01:39.327] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[21:01:39.327] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[21:01:39.327] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[21:01:39.327] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[21:01:39.328] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[21:01:39.328] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[21:01:39.328] [Il2CppAssemblyGenerator] Checking GameAssembly...
[21:01:39.502] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[21:01:39.503] Loading Mods...
[21:01:39.544] ------------------------------
[21:01:39.581] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[21:01:39.582] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[21:01:39.585] Melon Assembly loaded: '.\Mods\EditPlayerData.dll'
[21:01:39.585] SHA256 Hash: 'D0AE1A904057FF46AF12EA6A6856E28954B9DE4191999802288DA1DD00C4483D'
[21:01:39.586] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[21:01:39.586] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[21:01:39.592] Melon Assembly loaded: '.\Mods\IndustrialFarmer.dll'
[21:01:39.592] SHA256 Hash: '55C6358F421E23428D41BA902A7B3DC9D22F626234AD9CC3BAE27C032087F2D3'
[21:01:39.594] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[21:01:39.594] SHA256 Hash: 'C23277E37C12E91D3C47EB6A2548626CCE3111679FC4770DDEA2C06B5B77C9E9'
[21:01:39.598] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[21:01:39.598] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[21:01:39.680] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[21:01:39.680] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[21:01:39.690] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[21:01:39.691] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[21:01:40.279] ------------------------------
[21:01:40.280] BloonsTD6 Mod Helper v3.4.12
[21:01:40.280] by Gurrenm4 and Doombubbles
[21:01:40.280] Assembly: Btd6ModHelper.dll
[21:01:40.281] ------------------------------
[21:01:40.284] ------------------------------
[21:01:40.284] Ultimate Crosspathing v1.7.1
[21:01:40.284] by doombubbles
[21:01:40.284] Assembly: UltimateCrosspathing.dll
[21:01:40.284] ------------------------------
[21:01:40.288] ------------------------------
[21:01:40.288] EditPlayerData v1.5.1
[21:01:40.288] by MaliciousFiles
[21:01:40.288] Assembly: EditPlayerData.dll
[21:01:40.288] ------------------------------
[21:01:40.291] ------------------------------
[21:01:40.291] Faster Forward v1.1.5
[21:01:40.291] by doombubbles
[21:01:40.291] Assembly: FasterForward.dll
[21:01:40.291] ------------------------------
[21:01:40.294] ------------------------------
[21:01:40.294] Industrial Farmer v1.0.15
[21:01:40.294] by doombubbles
[21:01:40.294] Assembly: IndustrialFarmer.dll
[21:01:40.294] ------------------------------
[21:01:40.297] ------------------------------
[21:01:40.297] Lightning Monkey Mod v1.0.0
[21:01:40.297] by You
[21:01:40.297] Assembly: LightningMonkey.dll
[21:01:40.297] ------------------------------
[21:01:40.300] ------------------------------
[21:01:40.300] Powers in Shop v3.0.3
[21:01:40.300] by doombubbles
[21:01:40.300] Assembly: PowersInShop.dll
[21:01:40.301] ------------------------------
[21:01:40.304] ------------------------------
[21:01:40.304] Unlimited 5th Tiers + v1.1.9
[21:01:40.304] by doombubbles
[21:01:40.305] Assembly: Unlimited5thTiers.dll
[21:01:40.305] ------------------------------
[21:01:40.305] ------------------------------
[21:01:40.305] 8 Mods loaded.

[21:01:41.372] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[21:01:41.544] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[21:01:41.567] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[21:01:41.571] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[21:01:41.584] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[21:01:41.585] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[21:01:41.671] [Il2CppInterop] Method Void SetNumColumns(Int32, Int32[]) on type EditPlayerData.UI.ModHelperTable has unsupported parameter Int32[] colFlex of type System.Int32[]
[21:01:41.673] [Il2CppInterop] Method Void SetSetting(EditPlayerData.UI.PlayerDataSetting) on type EditPlayerData.UI.PlayerDataSettingDisplay has unsupported parameter EditPlayerData.UI.PlayerDataSetting setting of type EditPlayerData.UI.PlayerDataSetting
[21:01:41.993] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[21:01:42.535] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[21:01:42.772] [EditPlayerData] Melon Assembly loaded: 'C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Windows.Forms.dll'
[21:01:42.773] [EditPlayerData] SHA256 Hash: 'F8EE5BFDC1FE7508773CEB997F6C78FC8CF8CDBAE11F0DEB15E78AA94BEBFED7'
[21:01:42.774] [EditPlayerData] EditPlayerData loaded!
[21:01:42.997] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[21:01:43.493] [BloonsTD6_Mod_Helper] AlchemistLoader finished loading bytes
[21:01:43.605] [BloonsTD6_Mod_Helper] BananaFarmLoader finished loading bytes
[21:01:43.645] [BloonsTD6_Mod_Helper] BananaFarmerProLoader finished loading bytes
[21:01:43.839] [BloonsTD6_Mod_Helper] BeastHandlerLoader finished loading bytes
[21:01:43.946] [BloonsTD6_Mod_Helper] BombShooterLoader finished loading bytes
[21:01:44.007] [BloonsTD6_Mod_Helper] BoomerangMonkeyLoader finished loading bytes
[21:01:44.112] [BloonsTD6_Mod_Helper] DartMonkeyLoader finished loading bytes
[21:01:44.254] [BloonsTD6_Mod_Helper] DartlingGunnerLoader finished loading bytes
[21:01:44.370] [BloonsTD6_Mod_Helper] DesperadoLoader finished loading bytes
[21:01:44.515] [BloonsTD6_Mod_Helper] DruidLoader finished loading bytes
[21:01:44.757] [BloonsTD6_Mod_Helper] EngineerMonkeyLoader finished loading bytes
[21:01:44.840] [BloonsTD6_Mod_Helper] GlueGunnerLoader finished loading bytes
[21:01:45.012] [BloonsTD6_Mod_Helper] HeliPilotLoader finished loading bytes
[21:01:45.199] [BloonsTD6_Mod_Helper] IceMonkeyLoader finished loading bytes
[21:01:45.389] [BloonsTD6_Mod_Helper] MermonkeyLoader finished loading bytes
[21:01:45.492] [BloonsTD6_Mod_Helper] MonkeyAceLoader finished loading bytes
[21:01:45.703] [BloonsTD6_Mod_Helper] MonkeyBuccaneerLoader finished loading bytes
[21:01:45.857] [BloonsTD6_Mod_Helper] MonkeySubLoader finished loading bytes
[21:01:46.010] [BloonsTD6_Mod_Helper] MonkeyVillageLoader finished loading bytes
[21:01:46.106] [BloonsTD6_Mod_Helper] MortarMonkeyLoader finished loading bytes
[21:01:46.193] [BloonsTD6_Mod_Helper] NinjaMonkeyLoader finished loading bytes
[21:01:46.258] [BloonsTD6_Mod_Helper] SniperMonkeyLoader finished loading bytes
[21:01:46.359] [BloonsTD6_Mod_Helper] SpikeFactoryLoader finished loading bytes
[21:01:46.390] [BloonsTD6_Mod_Helper] SuperMonkeyBeaconLoader finished loading bytes
[21:01:46.728] [BloonsTD6_Mod_Helper] SuperMonkeyLoader finished loading bytes
[21:01:46.792] [BloonsTD6_Mod_Helper] TackShooterLoader finished loading bytes
[21:01:46.963] [BloonsTD6_Mod_Helper] WizardMonkeyLoader finished loading bytes
[21:01:49.173] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 356 mods over 6.6 seconds
[21:01:53.378] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[21:01:53.381] [BloonsTD6_Mod_Helper] Registering ModContent for Ultimate Crosspathing...
[21:01:53.440] [Ultimate_Crosspathing] Finished loading DartMonkeys!
[21:01:53.469] [Ultimate_Crosspathing] Finished loading BoomerangMonkeys!
[21:01:53.528] [Ultimate_Crosspathing] Finished loading BombShooters!
[21:01:53.552] [Ultimate_Crosspathing] Finished loading TackShooters!
[21:01:53.620] [Ultimate_Crosspathing] Finished loading IceMonkeys!
[21:01:53.675] [Ultimate_Crosspathing] Finished loading GlueGunners!
[21:01:53.719] [Ultimate_Crosspathing] Finished loading Desperados!
[21:01:53.742] [Ultimate_Crosspathing] Finished loading SniperMonkeys!
[21:01:53.794] [Ultimate_Crosspathing] Finished loading MonkeySubs!
[21:01:53.865] [Ultimate_Crosspathing] Finished loading MonkeyBuccaneers!
[21:01:53.914] [Ultimate_Crosspathing] Finished loading MonkeyAces!
[21:01:53.975] [Ultimate_Crosspathing] Finished loading HeliPilots!
[21:01:54.004] [Ultimate_Crosspathing] Finished loading MortarMonkeys!
[21:01:54.051] [Ultimate_Crosspathing] Finished loading DartlingGunners!
[21:01:54.110] [Ultimate_Crosspathing] Finished loading WizardMonkeys!
[21:01:54.206] [Ultimate_Crosspathing] Finished loading SuperMonkeys!
[21:01:54.316] [Ultimate_Crosspathing] Finished loading NinjaMonkeys!
[21:01:54.376] [Ultimate_Crosspathing] Finished loading Alchemists!
[21:01:54.428] [Ultimate_Crosspathing] Finished loading Druids!
[21:01:54.515] [Ultimate_Crosspathing] Finished loading Mermonkeys!
[21:01:54.555] [Ultimate_Crosspathing] Finished loading BananaFarms!
[21:01:54.618] [Ultimate_Crosspathing] Finished loading SpikeFactorys!
[21:01:54.668] [Ultimate_Crosspathing] Finished loading MonkeyVillages!
[21:01:54.716] [Ultimate_Crosspathing] Finished loading EngineerMonkeys!
[21:01:54.761] [Ultimate_Crosspathing] Finished loading BeastHandlers!
[21:01:54.774] [Ultimate_Crosspathing] Finished loading BananaFarmerPros!
[21:01:54.800] [Ultimate_Crosspathing] Finished loading SuperMonkeyBeacons!
[21:01:54.801] [BloonsTD6_Mod_Helper] Registering ModContent for EditPlayerData...
[21:01:54.801] [BloonsTD6_Mod_Helper] Registering ModContent for Industrial Farmer...
[21:01:54.854] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[21:01:55.000] [BloonsTD6_Mod_Helper] Registering ModContent for Powers in Shop...
[21:01:55.046] [BloonsTD6_Mod_Helper] Registering ModContent for Unlimited 5th Tiers +...
