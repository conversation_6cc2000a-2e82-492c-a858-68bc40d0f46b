using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
using Il2CppAssets.Scripts.Models.Towers.Behaviors;

namespace LightningMonkey.Towers.Upgrades
{
    // Bottom Path - Range/Utility Focus
    
    public class ExtendedRange : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => BOTTOM;
        public override int Tier => 1;
        public override int Cost => 250;
        public override string DisplayName => "Extended Range";
        public override string Description => "Lightning staff extends its reach. +15 range.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            towerModel.range += 15f;
        }
    }

    public class PrecisionStrike : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => BOTTOM;
        public override int Tier => 2;
        public override int Cost => 550;
        public override string DisplayName => "Precision Strike";
        public override string Description => "Lightning strikes with deadly precision. +1 damage, can see camo bloons.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var projectile = towerModel.GetAttackModel().weapons[0].projectile;
            projectile.GetDamageModel().damage += 1;

            // Add camo detection by adding targeting behavior
            towerModel.AddBehavior(new OverrideCamoDetectionModel("CamoDetection", true));
        }
    }

    public class QuickCast : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => BOTTOM;
        public override int Tier => 3;
        public override int Cost => 1200;
        public override string DisplayName => "Quick Cast";
        public override string Description => "Rapid lightning casting! Shoots 2 bolts at once, 30% faster attacks.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.7f; // 30% faster
            
            // Shoot 2 bolts
            weapon.emission = new ArcEmissionModel("", 2, 0, 15, null, false, false);
        }
    }

    public class SurgeCasting : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => BOTTOM;
        public override int Tier => 4;
        public override int Cost => 5800;
        public override string DisplayName => "Surge Casting";
        public override string Description => "Electrical surges allow rapid multi-casting! Shoots 3 bolts, +2 damage, +20 range.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            towerModel.range += 20f;
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.8f; // 20% faster
            weapon.projectile.GetDamageModel().damage += 2;
            
            // Shoot 3 bolts in arc
            weapon.emission = new ArcEmissionModel("", 3, 0, 25, null, false, false);
        }
    }

    public class OmnipresentStorm : ModUpgrade<LightningMonkeyTower>
    {
        public override int Path => BOTTOM;
        public override int Tier => 5;
        public override int Cost => 35000;
        public override string DisplayName => "Omnipresent Storm";
        public override string Description => "Lightning strikes everywhere at once! Global range, shoots 5 bolts, massive damage increase.";

        public override void ApplyUpgrade(TowerModel towerModel)
        {
            towerModel.range = 999f; // Global range
            var weapon = towerModel.GetAttackModel().weapons[0];
            weapon.Rate *= 0.6f; // 40% faster
            var projectile = weapon.projectile;
            projectile.GetDamageModel().damage += 4;
            projectile.pierce += 3;
            projectile.scale *= 1.5f;
            
            // Shoot 5 bolts in wide arc
            weapon.emission = new ArcEmissionModel("", 5, 0, 45, null, false, false);
        }
    }
}
