@echo off
echo Building Lightning Staff Monkey with real abilities (chain + black hole)...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"

if not exist "%UPGRADES_DIR%" mkdir "%UPGRADES_DIR%"

echo Writing tower core (Dart base + Druid lightning projectile)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Weapons;
echo using Il2CppAssets.Scripts.Unity;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.DartMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "Normal brown monkey wielding a mystical staff. Fires cyan lightning that can chain; at top tier, conjures a dark magic black hole.";
echo     public override int Cost =^> 700;
echo.
echo     // We actually provide 5/3/2 tiers below
	echo     public override int TopPathUpgrades =^> 5;
	echo     public override int MiddlePathUpgrades =^> 3;
	echo     public override int BottomPathUpgrades =^> 2;

echo     public override void ModifyBaseTowerModel^(TowerModel model^) {
	echo       // Slightly increase range for the staff
	echo       model.range += 15f;
	echo       var attack = model.GetAttackModel^(^);
	echo       var weapon = attack.weapons[0];
	echo       weapon.Rate = 0.9f;
	echo
	echo       // Use Druid lightning as the base projectile (cyan/teal)
	echo       var druid200 = Game.instance.model.GetTowerFromId^("Druid-200"^);
	echo       var lightning = druid200.GetAttackModel^(^).weapons[0].projectile.Duplicate^(^);
	echo       lightning.pierce = 4f;
	echo       lightning.GetDamageModel^(^).damage = 3f;
	echo       lightning.scale = 1.3f;
	echo
	echo       // Make sure it retains lightning arc visuals
	echo       var lm = lightning.GetBehavior^<LightningModel^>^(^);
	echo       if ^(lm == null^) {
	echo         lightning.AddBehavior^(new LightningModel^("LM", 3, 0.075f, 0f, 0, null, null, false, false, false^)^);
	echo       }
	echo       weapon.projectile = lightning;
	echo
	echo       // Optional: fire from hand area so it "looks" like staff casting
	echo       weapon.ejectX = 5f;
	echo       weapon.ejectY = 5f;
	echo       weapon.ejectZ = 0f;
	echo     }
	echo   }
	echo }
) > "%TOWER_FILE%"

echo Writing Chain Lightning upgrade (Middle T3)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Unity;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class ChainLightning : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 3;
echo     public override int Cost =^> 1500;
echo     public override string DisplayName =^> "Chain Lightning";
echo     public override string Description => Bolts arc between nearby bloons, chaining several times.; public override string Icon => ChainLightning_Icon;
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var proj = towerModel.GetAttackModel^(^).weapons[0].projectile;
echo       var lm = proj.GetBehavior^<LightningModel^>^(^);
echo       if ^(lm != null^) {
echo         lm.splits += 12;
echo         lm.splitRange += 18f;
echo       } else {
echo         var druid200 = Game.instance.model.GetTowerFromId^("Druid-200"^);
echo         var src = druid200.GetAttackModel^(^).weapons[0].projectile.GetBehavior^<LightningModel^>^(^).Duplicate^(^);
echo         proj.AddBehavior^(src^);
echo       }
echo       proj.pierce += 2;
echo       proj.GetDamageModel^(^).damage += 1;
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\ChainLightning.cs"

echo Writing Top-path black hole line (T1-T5 incl. Void Storm Master at T5)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo using Il2CppAssets.Scripts.Unity;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
	echo   public class DarkBoost : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 1; public override int Cost =^> 250; public override string DisplayName =^> "Dark Boost"; public override string Description =^> "+1 damage, +2 pierce."; public override void ApplyUpgrade^(TowerModel t^) { var p=t.GetAttackModel^(^).weapons[0].projectile; p.GetDamageModel^(^).damage+=1; p.pierce+=2; } }
	echo   public class ShadowStrike : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 2; public override int Cost =^> 500; public override string DisplayName =^> "Shadow Strike"; public override string Description =^> "15%% faster."; public override void ApplyUpgrade^(TowerModel t^) { t.GetAttackModel^(^).weapons[0].Rate *= 0.85f; } }
	echo   public class VoidChanneling : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 3; public override int Cost =^> 1200; public override string DisplayName =^> "Void Channeling"; public override string Description =^> "+2 dmg, +3 pierce."; public override void ApplyUpgrade^(TowerModel t^) { var p=t.GetAttackModel^(^).weapons[0].projectile; p.GetDamageModel^(^).damage+=2; p.pierce+=3; } }
	echo   public class DarkMastery : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> TOP; public override int Tier =^> 4; public override int Cost =^> 9000; public override string DisplayName =^> "Dark Mastery"; public override string Description =^> "Bigger bolts and faster cast."; public override void ApplyUpgrade^(TowerModel t^) { var a=t.GetAttackModel^(^); a.weapons[0].Rate *= 0.75f; a.weapons[0].projectile.scale *= 1.25f; } }
	echo   public class VoidStormMaster : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> {
		echo     public override int Path =^> TOP; public override int Tier =^> 5; public override int Cost =^> 45000;
		echo     public override string DisplayName =^> "Void Storm Master"; public override string Description =^> "On hit, conjures a vortex that sucks in bloons (uses Superstorm tornado logic).";
		echo     public override void ApplyUpgrade^(TowerModel tower^) {
		echo       var attack = tower^.GetAttackModel^(^);
		echo       var proj = attack.weapons[0].projectile;
		echo       // Clone Druid Superstorm tornado projectile as our black hole surrogate
		echo       var druid005 = Game.instance.model.GetTowerFromId^("Druid-005"^);
		echo       var tornado = druid005.GetAttackModel^(^).weapons[0].projectile.Duplicate^(^);
		echo       tornado.pierce = 999f;
		echo       tornado.GetDamageModel^(^).damage = 1f;
		echo       // Make it look darker/larger
		echo       tornado.scale *= 1.8f;
		echo       proj.AddBehavior^(new CreateProjectileOnContactModel^("BlackHole", tornado, new SingleEmissionModel^("", null^), true, false, false^)^);
		echo       // Also buff the main bolt to feel end-game
		echo       proj.GetDamageModel^(^).damage += 10f; proj.pierce += 10f; attack.weapons[0].Rate *= 0.6f;
		echo     }
	echo   }
	echo   // Middle path helper T1/T2
	echo   public class QuickCast : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> MIDDLE; public override int Tier =^> 1; public override int Cost =^> 150; public override string DisplayName =^> "Quick Cast"; public override string Description =^> "15%% faster"; public override void ApplyUpgrade^(TowerModel t^) { t.GetAttackModel^(^).weapons[0].Rate *= 0.85f; } }
	echo   public class SurgeCasting : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> MIDDLE; public override int Tier =^> 2; public override int Cost =^> 300; public override string DisplayName =^> "Surge Casting"; public override string Description =^> "20%% faster"; public override void ApplyUpgrade^(TowerModel t^) { t.GetAttackModel^(^).weapons[0].Rate *= 0.8f; } }
	echo   public class ExtendedRange : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> BOTTOM; public override int Tier =^> 1; public override int Cost =^> 180; public override string DisplayName =^> "Extended Range"; public override string Description =^> "+10 range"; public override void ApplyUpgrade^(TowerModel t^) { t.range += 10f; } }
	echo   public class PrecisionStrike : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override int Path =^> BOTTOM; public override int Tier =^> 2; public override int Cost =^> 350; public override string DisplayName =^> "Precision Strike"; public override string Description =^> "+1 dmg, +8 range"; public override void ApplyUpgrade^(TowerModel t^) { t.range += 8f; t.GetAttackModel^(^).weapons[0].projectile.GetDamageModel^(^).damage += 1; } }
	echo }
) > "%UPGRADES_DIR%\AllUpgrades.cs"

echo Building complete Lightning Staff Monkey...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Staff Monkey built.
    echo ========================================
    echo - Base: Dart Monkey visuals (normal brown monkey)
    echo - Projectile: Druid lightning (cyan/teal), spawns from hand area
    echo - Middle T3: Chain Lightning (real LightningModel splits)
    echo - Top T5: Void Storm Master (uses Superstorm tornado as black hole)
    echo - Tiers: Top 5 / Middle 3 / Bottom 2 (matches code)
    echo.
    echo Note: Attaching Geraldo's staff visually requires a custom display/asset bundle. We can add that next if you want.
) else (
    echo Build failed. Check errors above.
)
pause



