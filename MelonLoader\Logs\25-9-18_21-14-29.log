
[21:14:29.934] ------------------------------
[21:14:29.936] MelonLoader v0.7.1 Open-Beta
[21:14:29.937] OS: Windows 11
[21:14:29.938] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[21:14:29.938] ------------------------------
[21:14:29.939] Game Type: Il2cpp
[21:14:29.939] Game Arch: x64
[21:14:29.939] ------------------------------
[21:14:29.939] Command-Line: 
[21:14:29.939] ------------------------------
[21:14:29.939] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:14:29.940] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:14:29.940] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[21:14:29.940] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[21:14:29.940] Runtime Type: net6
[21:14:30.003] ------------------------------
[21:14:30.004] Game Name: BloonsTD6
[21:14:30.004] Game Developer: Ninja Kiwi
[21:14:30.006] Unity Version: 6000.0.52f1
[21:14:30.006] Game Version: 50.2
[21:14:30.006] ------------------------------

[21:14:30.429] Preferences Loaded!

[21:14:30.442] Loading UserLibs...
[21:14:30.444] 0 UserLibs loaded.

[21:14:30.444] Loading Plugins...
[21:14:30.449] 0 Plugins loaded.

[21:14:30.835] Loading Il2CppAssemblyGenerator...
[21:14:30.873] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[21:14:31.044] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[21:14:31.044] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[21:14:31.045] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[21:14:31.045] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[21:14:31.051] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[21:14:31.051] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[21:14:31.051] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[21:14:31.051] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[21:14:31.051] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[21:14:31.051] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[21:14:31.052] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[21:14:31.052] [Il2CppAssemblyGenerator] Checking GameAssembly...
[21:14:31.222] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[21:14:31.223] Loading Mods...
[21:14:31.271] ------------------------------
[21:14:31.313] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[21:14:31.313] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[21:14:31.326] Melon Assembly loaded: '.\Mods\CardMonkey.dll'
[21:14:31.326] SHA256 Hash: '1B332E160A11A29E520A4AE7D4AF9C614829BB46D11765D2F13A01AD4DE3F179'
[21:14:31.327] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[21:14:31.328] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[21:14:31.333] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[21:14:31.333] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[21:14:31.411] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[21:14:31.412] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[21:14:31.418] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[21:14:31.418] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'
