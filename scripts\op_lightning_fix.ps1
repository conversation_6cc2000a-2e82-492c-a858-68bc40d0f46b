$ErrorActionPreference = 'Stop'
$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$lt = Join-Path $root 'Towers\LightningMonkey.cs'
$upg = Join-Path $root 'Towers\Upgrades\AllUpgrades.cs'

# Overwrite LightningMonkey.cs with OP dual-weapon and huge range (safe properties only)
$lm = @(
'using BTD_Mod_Helper.Api.Towers;',
'using BTD_Mod_Helper.Extensions;',
'using Il2CppAssets.Scripts.Models.Towers;',
'using Il2CppAssets.Scripts.Models.TowerSets;',
'using Il2CppAssets.Scripts.Models.Towers.Weapons;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;',
'using Il2CppAssets.Scripts.Unity;',
'',
'namespace LightningMonkey.Towers',
'{',
'    public class LightningMonkeyTower : ModTower',
'    {',
'        public override string BaseTower => TowerType.Druid;',
'        public override TowerSet TowerSet => TowerSet.Magic;',
'',
'        public override string DisplayName => "Lightning Monkey";',
'        public override string Description => "Unleashes colossal lightning and void vortices.";',
'        public override int Cost => 500;',
'        public override string Icon => "LightningMonkey-Icon";',
'        public override string Portrait => "LightningMonkey-Portrait";',
'',
'        public override int TopPathUpgrades => 5;',
'        public override int MiddlePathUpgrades => 3;',
'        public override int BottomPathUpgrades => 2;',
'',
'        public override void ModifyBaseTowerModel(TowerModel towerModel)',
'        {',
'            // Massive base range',
'            towerModel.range += 120f;',
'',
'            var attack = towerModel.GetAttackModel();',
'            if (attack == null) return;',
'',
'            var baseWeapon = attack.weapons[0];',
'            baseWeapon.Rate = 0.4f; // faster lightning',
'',
'            // BOOSTED Lightning projectile from Druid-200',
'            try',
'            {',
'                var druid200 = Game.instance.model.GetTowerFromId("Druid-200");',
'                if (druid200 != null)',
'                {',
'                    var druidAttack = druid200.GetAttackModel();',
'                    ProjectileModel source = null;',
'                    foreach (var w in druidAttack.weapons)',
'                    {',
'                        if (w.projectile.GetBehavior<LightningModel>() != null)',
'                        {',
'                            source = w.projectile;',
'                            break;',
'                        }',
'                    }',
'                    if (source != null)',
'                    {',
'                        var lightning = source.Duplicate();',
'                        lightning.pierce = 50f;',
'                        var dmg = lightning.GetDamageModel(); if (dmg != null) dmg.damage = 20f;',
'                        lightning.scale = 1.75f;',
'                        baseWeapon.projectile = lightning;',
'                    }',
'                }',
'            }',
'            catch { }',
'',
'            // OP Black Hole weapon based on Druid-005 Superstorm tornado',
'            try',
'            {',
'                var druid005 = Game.instance.model.GetTowerFromId("Druid-005");',
'                if (druid005 != null)',
'                {',
'                    var tornadoProj = druid005.GetAttackModel().weapons[0].projectile.Duplicate();',
'                    tornadoProj.pierce = 999f;',
'                    var tDmg = tornadoProj.GetDamageModel(); if (tDmg != null) tDmg.damage = 50f;',
'                    tornadoProj.scale *= 3.0f;',
'                    var blackHoleWeapon = baseWeapon.Duplicate();',
'                    blackHoleWeapon.name = "BlackHoleWeapon";',
'                    blackHoleWeapon.projectile = tornadoProj;',
'                    blackHoleWeapon.Rate = 0.15f; // very frequent vortex',
'                    attack.AddWeapon(blackHoleWeapon);',
'                }',
'            }',
'            catch { }',
'        }',
'    }',
'}'
)
[IO.File]::WriteAllLines($lt, $lm)

# Overwrite AllUpgrades.cs with OP stats and robust Fire God aura without direct DoT type refs
$up = @(
'using BTD_Mod_Helper.Api.Towers;',
'using BTD_Mod_Helper.Extensions;',
'using Il2CppAssets.Scripts.Models.Towers;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;',
'using Il2CppAssets.Scripts.Unity;',
'',
'namespace LightningMonkey.Towers.Upgrades {',
'  // Top Path',
'  public class DarkBoost : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 1; public override int Cost => 150;',
'    public override string DisplayName => "Dark Boost"; public override string Description => "+10 dmg, +20 pierce.";',
'    public override void ApplyUpgrade(TowerModel t) { var p=t.GetAttackModel().weapons[0].projectile; p.GetDamageModel().damage+=10; p.pierce+=20; }',
'  }',
'  public class ShadowStrike : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 2; public override int Cost => 300;',
'    public override string DisplayName => "Shadow Strike"; public override string Description => "40% faster.";',
'    public override void ApplyUpgrade(TowerModel t) { t.GetAttackModel().weapons[0].Rate *= 0.6f; }',
'  }',
'  public class VoidChanneling : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 3; public override int Cost => 800;',
'    public override string DisplayName => "Void Channeling"; public override string Description => "+20 dmg, +30 pierce.";',
'    public override void ApplyUpgrade(TowerModel t) { var p=t.GetAttackModel().weapons[0].projectile; p.GetDamageModel().damage+=20; p.pierce+=30; }',
'  }',
'  public class DarkMastery : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 4; public override int Cost => 6000;',
'    public override string DisplayName => "Dark Mastery"; public override string Description => "Bigger bolts and faster cast.";',
'    public override void ApplyUpgrade(TowerModel t) { var a=t.GetAttackModel(); a.weapons[0].Rate *= 0.7f; a.weapons[0].projectile.scale *= 1.35f; }',
'  }',
'  // T5 Fire God: OP aura (simulated stacking by adding multiple aura attacks with high DPS)',
'  public class FireGod : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 5; public override int Cost => 30000;',
'    public override string DisplayName => "Fire God";',
'    public override string Description => "Transforms into a blazing deity with a massive burning aura.";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      var wiz520 = Game.instance.model.GetTowerFromId("WizardMonkey-520"); if (wiz520 != null) t.display = wiz520.display; t.displayScale *= 1.3f;',
'      var inferno = Game.instance.model.GetTowerFromId("TackShooter-520");',
'      if (inferno != null) {',
'        // Create 5 aura copies to simulate stacking/insane DPS and coverage',
'        for (int i=0;i<5;i++) {',
'          var aura = inferno.GetAttackModel().Duplicate();',
'          aura.name = "FireGodAura"+i; aura.range = t.range;',
'          var w = aura.weapons[0]; w.Rate = 0.1f;',
'          var proj = w.projectile; var d = proj.GetDamageModel(); if (d != null) d.damage = 18f; proj.pierce = 9999f; proj.scale *= 2.2f;',
'          t.AddBehavior(aura);',
'        }',
'      }',
'    }',
'  }',
'',
'  // Middle Path',
'  public class QuickCast : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => MIDDLE; public override int Tier => 1; public override int Cost => 100;',
'    public override string DisplayName => "Quick Cast"; public override string Description => "30% faster";',
'    public override void ApplyUpgrade(TowerModel t) { t.GetAttackModel().weapons[0].Rate *= 0.7f; }',
'  }',
'  public class SurgeCasting : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => MIDDLE; public override int Tier => 2; public override int Cost => 200;',
'    public override string DisplayName => "Surge Casting"; public override string Description => "35% faster";',
'    public override void ApplyUpgrade(TowerModel t) { t.GetAttackModel().weapons[0].Rate *= 0.65f; }',
'  }',
'  public class ChainLightning : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => MIDDLE; public override int Tier => 3; public override int Cost => 1000;',
'    public override string DisplayName => "Chain Lightning";',
'    public override void ApplyUpgrade(TowerModel towerModel) { var proj = towerModel.GetAttackModel().weapons[0].projectile; var lm = proj.GetBehavior<LightningModel>(); if (lm != null) { lm.splits += 30; lm.splitRange += 40f; } proj.pierce += 30; proj.GetDamageModel().damage += 15; }',
'  }',
'',
'  // Bottom Path (range buffs)',
'  public class ExtendedRange : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => BOTTOM; public override int Tier => 1; public override int Cost => 120;',
'    public override string DisplayName => "Extended Range"; public override string Description => "+100 range";',
'    public override void ApplyUpgrade(TowerModel t) { t.range += 100f; }',
'  }',
'  public class PrecisionStrike : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => BOTTOM; public override int Tier => 2; public override int Cost => 200;',
'    public override string DisplayName => "Precision Strike"; public override string Description => "+1 dmg, +120 range";',
'    public override void ApplyUpgrade(TowerModel t) { t.range += 120f; t.GetAttackModel().weapons[0].projectile.GetDamageModel().damage += 1; }',
'  }',
'}'
)
[IO.File]::WriteAllLines($upg, $up)

Write-Host 'Applied OP fixes: compiled-safe black hole buffs, massive range, and heavy Fire God aura stacking.'

