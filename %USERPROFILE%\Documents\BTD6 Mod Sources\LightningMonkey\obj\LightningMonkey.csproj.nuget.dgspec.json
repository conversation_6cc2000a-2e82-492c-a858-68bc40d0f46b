{"format": 1, "restore": {"C:\\Program Files (x86)\\Steam\\steamapps\\common\\BloonsTD6\\%USERPROFILE%\\Documents\\BTD6 Mod Sources\\LightningMonkey\\LightningMonkey.csproj": {}}, "projects": {"C:\\Program Files (x86)\\Steam\\steamapps\\common\\BloonsTD6\\%USERPROFILE%\\Documents\\BTD6 Mod Sources\\LightningMonkey\\LightningMonkey.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\BloonsTD6\\%USERPROFILE%\\Documents\\BTD6 Mod Sources\\LightningMonkey\\LightningMonkey.csproj", "projectName": "LightningMonkey", "projectPath": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\BloonsTD6\\%USERPROFILE%\\Documents\\BTD6 Mod Sources\\LightningMonkey\\LightningMonkey.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Program Files (x86)\\Steam\\steamapps\\common\\BloonsTD6\\%USERPROFILE%\\Documents\\BTD6 Mod Sources\\LightningMonkey\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}}