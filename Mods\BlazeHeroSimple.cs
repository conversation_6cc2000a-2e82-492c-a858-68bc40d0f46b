using MelonLoader;
using BTD_Mod_Helper;
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Attack;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Unity;
using BTD_Mod_Helper.Api.Enums;

[assembly: MelonInfo(typeof(BlazeHeroSimple.Main), "Blaze Hero", "1.0.0", "YourName")]
[assembly: <PERSON>on<PERSON><PERSON>("Ninja Kiwi", "BloonsTD6")]

namespace BlazeHeroSimple
{
    public class Main : BloonsTD6Mod
    {
        public override void OnApplicationStart()
        {
            MelonLogger.Msg("🔥 BLAZE HERO LOADED! The flame chaser is ready! 🔥");
        }
    }

    public class Blaze : ModHero
    {
        public override string BaseTower => TowerType.Quincy;
        public override int Cost => 750;
        public override string Description => "🔥 BLAZE - The Flame Chaser! A mobile flamethrowing hero who hunts down escaping balloons with devastating blue fire attacks! 🔥";
        public override string DisplayName => "Blaze";
        public override string Name => "Blaze";
        public override string Title => "The Flame Chaser";
        public override string Level1Description => "A fearless scarlet monkey with a flamethrower who NEVER lets a balloon escape!";

        public override void ModifyBaseTowerModel(TowerModel towerModel)
        {
            // Make Blaze a powerful flamethrower hero
            towerModel.range = 60f;
            towerModel.cost = 750f;
            
            var attackModel = towerModel.GetAttackModel();
            attackModel.range = 60f;
            
            // Use Wizard's fireball as base but make it much better
            var wizardTower = Game.instance.model.GetTowerFromId("WizardMonkey-020");
            var fireballProjectile = wizardTower.GetAttackModel().weapons[0].projectile.Duplicate();
            
            var weaponModel = attackModel.weapons[0];
            weaponModel.projectile = fireballProjectile;
            weaponModel.rate = 0.08f; // Very fast attack rate
            
            // Enhance the flame projectile
            var projectileModel = weaponModel.projectile;
            projectileModel.id = "BlazeFlameProjectile";
            projectileModel.maxPierce = 15f;
            projectileModel.pierce = 15f;
            projectileModel.radius = 15f;
            
            // Boost damage
            var damageModel = projectileModel.GetDamageModel();
            damageModel.damage = 4f;
            
            // Add strong burning effect
            var burnEffect = new DamageOverTimeModel("BlazeBurn", 3f, 5f, false, false, false, false, false);
            projectileModel.AddBehavior(burnEffect);
            
            // Make flames seek targets aggressively (simulates chasing)
            var seekingBehavior = new TrackTargetModel("BlazeHoming", 999f, true, true, 360f, false, 999f, false, false);
            projectileModel.AddBehavior(seekingBehavior);
            
            // Add explosion on contact for extra damage
            var bombProjectile = Game.instance.model.GetTowerFromId("BombShooter").GetAttackModel().weapons[0].projectile.Duplicate();
            bombProjectile.GetDamageModel().damage = 2f;
            bombProjectile.radius = 10f;
            
            var explosionBehavior = new CreateProjectileOnContactModel("BlazeExplosion", bombProjectile, 
                new Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions.SingleEmissionModel("", null), true, false, false);
            projectileModel.AddBehavior(explosionBehavior);
        }

        public override int MaxLevel => 20;

        public override void ModifyTowerModelForLevel(TowerModel towerModel, int level)
        {
            var attackModel = towerModel.GetAttackModel();
            var weaponModel = attackModel.weapons[0];
            var projectileModel = weaponModel.projectile;
            var damageModel = projectileModel.GetDamageModel();

            switch (level)
            {
                case 2:
                    damageModel.damage += 2f;
                    towerModel.range += 10f;
                    attackModel.range += 10f;
                    break;
                    
                case 3:
                    // Even faster flames!
                    weaponModel.rate *= 0.8f;
                    projectileModel.pierce += 5f;
                    projectileModel.maxPierce += 5f;
                    break;
                    
                case 4:
                    // Blue flames can melt anything!
                    damageModel.immuneBloonProperties = Il2Cpp.BloonProperties.None;
                    var burnEffect = projectileModel.GetBehavior<DamageOverTimeModel>();
                    if (burnEffect != null) burnEffect.damage += 1f;
                    break;
                    
                case 5:
                    damageModel.damage += 3f;
                    projectileModel.radius += 5f;
                    break;
                    
                case 6:
                    // Better seeking - chases more aggressively
                    var seeking = projectileModel.GetBehavior<TrackTargetModel>();
                    if (seeking != null)
                    {
                        seeking.distance = 999f;
                        seeking.constantlyAquireNewTarget = true;
                        seeking.maxSeekAngle = 360f;
                    }
                    break;
                    
                case 7:
                    weaponModel.rate *= 0.7f; // Even faster
                    damageModel.damage += 2f;
                    break;
                    
                case 8:
                    // Explosive upgrade
                    var explosion = projectileModel.GetBehavior<CreateProjectileOnContactModel>();
                    if (explosion != null)
                    {
                        explosion.projectile.GetDamageModel().damage += 3f;
                        explosion.projectile.radius += 5f;
                    }
                    break;
                    
                case 10:
                    // Massive range - can chase across entire map!
                    towerModel.range = 300f;
                    attackModel.range = 300f;
                    damageModel.damage += 5f;
                    weaponModel.rate *= 0.6f;
                    break;
                    
                case 12:
                    // Inferno mode
                    damageModel.damage += 8f;
                    projectileModel.pierce += 10f;
                    projectileModel.maxPierce += 10f;
                    var burnEffect2 = projectileModel.GetBehavior<DamageOverTimeModel>();
                    if (burnEffect2 != null) 
                    {
                        burnEffect2.damage += 2f;
                        burnEffect2.lifespan += 3f;
                    }
                    break;
                    
                case 15:
                    // Phoenix form - massive flames
                    damageModel.damage += 10f;
                    projectileModel.radius += 10f;
                    weaponModel.rate *= 0.5f; // Super fast
                    break;
                    
                case 18:
                    // Plasma flames
                    damageModel.damage += 15f;
                    towerModel.range = 500f;
                    attackModel.range = 500f;
                    break;
                    
                case 20:
                    // ULTIMATE BLAZE - Apocalypse mode!
                    damageModel.damage = 100f; // Massive damage
                    towerModel.range = 999f; // Entire map
                    attackModel.range = 999f;
                    weaponModel.rate = 0.02f; // Insanely fast
                    projectileModel.pierce = 999f;
                    projectileModel.maxPierce = 999f;
                    projectileModel.radius = 50f; // Huge explosions
                    
                    // Ultimate burn effect
                    var ultimateBurn = projectileModel.GetBehavior<DamageOverTimeModel>();
                    if (ultimateBurn != null)
                    {
                        ultimateBurn.damage = 20f;
                        ultimateBurn.lifespan = 10f;
                    }
                    
                    // Ultimate explosion
                    var ultimateExplosion = projectileModel.GetBehavior<CreateProjectileOnContactModel>();
                    if (ultimateExplosion != null)
                    {
                        ultimateExplosion.projectile.GetDamageModel().damage = 50f;
                        ultimateExplosion.projectile.radius = 100f;
                    }
                    break;
            }
        }
    }
}
