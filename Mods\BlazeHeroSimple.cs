using MelonLoader;
using BTD_Mod_Helper;
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Attack;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Unity;
using BTD_Mod_Helper.Api.Enums;

[assembly: MelonInfo(typeof(BlazeHeroSimple.Main), "Blaze Hero", "1.0.0", "YourName")]
[assembly: <PERSON><PERSON><PERSON><PERSON>("Ninja Kiwi", "BloonsTD6")]

namespace BlazeHeroSimple
{
    public class Main : BloonsTD6Mod
    {
        public override void OnApplicationStart()
        {
            MelonLogger.Msg("💀🔥 THE VOID FLAME REAPER HAS AWAKENED! BLAZE RISES FROM THE DARKNESS! 🔥💀");
        }
    }

    public class Blaze : ModHero
    {
        public override string BaseTower => TowerType.Quincy;
        public override int Cost => 666; // Demonic cost for a demonic hero
        public override string Description => "💀🔥 BLAZE - The Void Flame Reaper! A hooded figure wreathed in darkness and blazing fire, who emerges from the shadows to incinerate any balloon that dares escape! The void consumes, the flames devour! 🔥💀";
        public override string DisplayName => "Blaze";
        public override string Name => "Blaze";
        public override string Title => "The Void Flame Reaper";
        public override string Level1Description => "A terrifying hooded figure cloaked in void-black shadows, with piercing flame-orange eyes and a weapon that channels the fury of a thousand burning suns!";

        public override void ModifyBaseTowerModel(TowerModel towerModel)
        {
            // Make Blaze a terrifying void flame reaper
            towerModel.range = 80f; // Increased range for a fearsome hero
            towerModel.cost = 666f; // Demonic cost

            var attackModel = towerModel.GetAttackModel();
            attackModel.range = 80f;

            // Use multiple projectile sources for epic effect
            // Base: Wizard's Phoenix (most epic fire effect)
            var phoenixTower = Game.instance.model.GetTowerFromId("WizardMonkey-050");
            var phoenixProjectile = phoenixTower.GetAttackModel().weapons[0].projectile.Duplicate();

            var weaponModel = attackModel.weapons[0];
            weaponModel.projectile = phoenixProjectile;
            weaponModel.rate = 0.05f; // Insanely fast attack rate

            // Create the VOID FLAME projectile
            var projectileModel = weaponModel.projectile;
            projectileModel.id = "VoidFlameProjectile";
            projectileModel.maxPierce = 25f; // Pierces through many balloons
            projectileModel.pierce = 25f;
            projectileModel.radius = 20f; // Large explosion radius

            // MASSIVE damage for a fearsome hero
            var damageModel = projectileModel.GetDamageModel();
            damageModel.damage = 8f; // High base damage

            // Add VOID BURN - stronger than normal fire
            var voidBurnEffect = new DamageOverTimeModel("VoidBurn", 5f, 8f, false, false, false, false, false);
            projectileModel.AddBehavior(voidBurnEffect);

            // AGGRESSIVE seeking - the void hunts relentlessly
            var voidSeekingBehavior = new TrackTargetModel("VoidHunting", 999f, true, true, 360f, false, 999f, false, false);
            projectileModel.AddBehavior(voidSeekingBehavior);

            // Add MASSIVE explosion on contact
            var explosiveProjectile = Game.instance.model.GetTowerFromId("BombShooter-500").GetAttackModel().weapons[0].projectile.Duplicate();
            explosiveProjectile.GetDamageModel().damage = 10f; // Huge explosion damage
            explosiveProjectile.radius = 25f; // Massive explosion radius

            var voidExplosionBehavior = new CreateProjectileOnContactModel("VoidExplosion", explosiveProjectile,
                new Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions.SingleEmissionModel("", null), true, false, false);
            projectileModel.AddBehavior(voidExplosionBehavior);

            // Add secondary flame trail effect
            var flameTrailProjectile = Game.instance.model.GetTowerFromId("WizardMonkey-020").GetAttackModel().weapons[0].projectile.Duplicate();
            flameTrailProjectile.GetDamageModel().damage = 3f;
            flameTrailProjectile.radius = 15f;

            var flameTrailBehavior = new CreateProjectileOnExhaustFractionModel("FlameTrail", flameTrailProjectile,
                new Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions.SingleEmissionModel("", null), 0.1f, 0.1f, true, false, false);
            projectileModel.AddBehavior(flameTrailBehavior);
        }

        public override int MaxLevel => 20;

        // EPIC level progression - The Void Flame Reaper grows in power!
        public override void ModifyTowerModelForLevel(TowerModel towerModel, int level)
        {
            var attackModel = towerModel.GetAttackModel();
            var weaponModel = attackModel.weapons[0];
            var projectileModel = weaponModel.projectile;
            var damageModel = projectileModel.GetDamageModel();

            switch (level)
            {
                case 2:
                    // The void grows stronger
                    damageModel.damage += 4f;
                    towerModel.range += 20f;
                    attackModel.range += 20f;
                    break;

                case 3:
                    // Flames of fury unleashed!
                    weaponModel.rate *= 0.6f; // Much faster
                    projectileModel.pierce += 10f;
                    projectileModel.maxPierce += 10f;
                    break;

                case 4:
                    // Void flames consume ALL!
                    damageModel.immuneBloonProperties = Il2Cpp.BloonProperties.None;
                    var burnEffect = projectileModel.GetBehavior<DamageOverTimeModel>();
                    if (burnEffect != null)
                    {
                        burnEffect.damage += 3f;
                        burnEffect.lifespan += 3f;
                    }
                    break;

                case 5:
                    // Growing inferno
                    damageModel.damage += 6f;
                    projectileModel.radius += 5f;
                    break;
                    
                case 6:
                    // THE VOID HUNTS - Enhanced seeking
                    var seeking = projectileModel.GetBehavior<TrackTargetModel>();
                    if (seeking != null)
                    {
                        seeking.distance = 999f;
                        seeking.constantlyAquireNewTarget = true;
                        seeking.maxSeekAngle = 360f;
                    }
                    damageModel.damage += 4f;
                    break;

                case 7:
                    // Flames of vengeance
                    weaponModel.rate *= 0.5f; // Insanely fast
                    damageModel.damage += 6f;
                    projectileModel.radius += 5f;
                    break;

                case 8:
                    // VOID EXPLOSIONS
                    var explosion = projectileModel.GetBehavior<CreateProjectileOnContactModel>();
                    if (explosion != null)
                    {
                        explosion.projectile.GetDamageModel().damage += 8f;
                        explosion.projectile.radius += 10f;
                    }
                    break;

                case 10:
                    // SHADOW REALM RANGE - can hunt across dimensions!
                    towerModel.range = 400f;
                    attackModel.range = 400f;
                    damageModel.damage += 10f;
                    weaponModel.rate *= 0.4f; // Supernatural speed
                    projectileModel.pierce += 15f;
                    projectileModel.maxPierce += 15f;
                    break;
                    
                case 12:
                    // VOID INFERNO - The darkness burns eternal
                    damageModel.damage += 15f;
                    projectileModel.pierce += 20f;
                    projectileModel.maxPierce += 20f;
                    var burnEffect2 = projectileModel.GetBehavior<DamageOverTimeModel>();
                    if (burnEffect2 != null)
                    {
                        burnEffect2.damage += 5f;
                        burnEffect2.lifespan += 5f;
                    }
                    break;

                case 15:
                    // SHADOW PHOENIX FORM - Reborn from the void
                    damageModel.damage += 20f;
                    projectileModel.radius += 15f;
                    weaponModel.rate *= 0.3f; // Supernatural speed
                    towerModel.range = 600f;
                    attackModel.range = 600f;
                    break;

                case 18:
                    // VOID PLASMA FLAMES - Reality-melting fire
                    damageModel.damage += 30f;
                    towerModel.range = 800f;
                    attackModel.range = 800f;
                    projectileModel.pierce += 50f;
                    projectileModel.maxPierce += 50f;
                    break;

                case 20:
                    // 💀🔥 ULTIMATE: THE VOID FLAME APOCALYPSE! 🔥💀
                    damageModel.damage = 666f; // DEMONIC DAMAGE!
                    towerModel.range = 999f; // OMNIPRESENT
                    attackModel.range = 999f;
                    weaponModel.rate = 0.01f; // GODLIKE SPEED
                    projectileModel.pierce = 999f; // INFINITE PIERCE
                    projectileModel.maxPierce = 999f;
                    projectileModel.radius = 100f; // MASSIVE EXPLOSIONS

                    // ULTIMATE VOID BURN - Burns for eternity
                    var ultimateBurn = projectileModel.GetBehavior<DamageOverTimeModel>();
                    if (ultimateBurn != null)
                    {
                        ultimateBurn.damage = 100f; // Insane burn damage
                        ultimateBurn.lifespan = 20f; // Burns forever
                    }

                    // ULTIMATE VOID EXPLOSION - Reality-shattering
                    var ultimateExplosion = projectileModel.GetBehavior<CreateProjectileOnContactModel>();
                    if (ultimateExplosion != null)
                    {
                        ultimateExplosion.projectile.GetDamageModel().damage = 500f; // APOCALYPTIC
                        ultimateExplosion.projectile.radius = 200f; // SCREEN-WIDE EXPLOSIONS
                    }
                    break;
            }
        }
    }
}
