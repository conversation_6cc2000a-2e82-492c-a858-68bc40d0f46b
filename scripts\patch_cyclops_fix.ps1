$ErrorActionPreference = 'Stop'
$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$tdir = Join-Path $root 'Towers'
$updir = Join-Path $tdir 'Upgrades'

# Fix Cyclops.cs camo detection (use Village-020 behavior instead of FilterInvisibleModel)
$cyclopsPath = Join-Path $tdir 'Cyclops.cs'
if (-not (Test-Path -LiteralPath $cyclopsPath)) { throw "Missing $cyclopsPath" }
$src = Get-Content -Raw -LiteralPath $cyclopsPath
$src = $src -replace '(?s)// Inherent camo detection:[\s\S]*?\}', @'
      // Inherent camo detection via built-in camo-revealing support aura (Radar Scanner)
      try {
        var v020 = Game.instance.model.GetTowerFromId("MonkeyVillage-020");
        if (v020 != null) {
          foreach (var b in v020.behaviors) t.AddBehavior(b.Duplicate());
        }
      } catch {}
    }
  }
}
'@
Set-Content -LiteralPath $cyclopsPath -Encoding UTF8 -Value $src

# Update some visual progression displays on higher tiers
function Patch-UpgradeFile {
  param([string]$file,[string]$pattern,[string]$inject)
  if (-not (Test-Path -LiteralPath $file)) { return }
  $c = Get-Content -Raw -LiteralPath $file
  if ($c -notmatch [regex]::Escape($inject)) {
    $c = $c -replace $pattern, "${inject}`n$0"
    Set-Content -LiteralPath $file -Encoding UTF8 -Value $c
  }
}

# Top T4/T5 displays (use SuperMonkey displays)
$topFile = Join-Path $updir 'CyclopsTop.cs'
$injectT4 = 'var sm400 = Game.instance.model.GetTowerFromId("SuperMonkey-400"); if (sm400 != null) t.display = sm400.display;'
$injectT5 = 'var sm520 = Game.instance.model.GetTowerFromId("SuperMonkey-520"); if (sm520 != null) t.display = sm520.display;'
Patch-UpgradeFile -file $topFile -pattern 'public override void ApplyUpgrade\(TowerModel t\) \{[\s\S]*?\}' -inject $injectT4
Patch-UpgradeFile -file $topFile -pattern 'public class OmegaLevelMutant[\s\S]*?ApplyUpgrade\(TowerModel t\) \{[\s\S]*?\}' -inject $injectT5

# Middle T5 display bump (scale)
$midFile = Join-Path $updir 'CyclopsMiddle.cs'
$injectMid = 't.displayScale *= 1.15f;'
Patch-UpgradeFile -file $midFile -pattern 'public class XMenLeader[\s\S]*?ApplyUpgrade\(TowerModel t\) \{[\s\S]*?\}' -inject $injectMid

# Bottom T5 Phoenix display (Wizard-520)
$botFile = Join-Path $updir 'CyclopsBottom.cs'
$injectBot = 'var wiz520 = Game.instance.model.GetTowerFromId("WizardMonkey-520"); if (wiz520 != null) t.display = wiz520.display;'
Patch-UpgradeFile -file $botFile -pattern 'public class PhoenixForceCyclops[\s\S]*?ApplyUpgrade\(TowerModel t\) \{[\s\S]*?\}' -inject $injectBot

Write-Host 'Patched Cyclops: camo via v020 aura and added visual progression on top/mid/bot tiers.'

