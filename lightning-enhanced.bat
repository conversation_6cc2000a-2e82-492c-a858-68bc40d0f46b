@echo off
echo Creating enhanced Lightning Monkey with proper visuals...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

echo Writing enhanced Lightning Monkey tower...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.WizardMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "Channels crackling lightning bolts that chain through bloons!";
echo     public override int Cost =^> 550;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 15f;
echo       var atk = m.GetAttackModel^(^);
echo       
echo       // Enhanced lightning stats
echo       atk.weapons[0].Rate = 0.8f; // Faster than wizard
echo       var p = atk.weapons[0].projectile;
echo       p.pierce += 3;
echo       p.GetDamageModel^(^).damage = 3;
echo       
echo       // Make projectile look more like lightning
echo       p.display = new Il2CppNinjaKiwi.Common.ResourceUtils.PrefabReference^(^) { guidRef = "LightningBolt" };
echo       p.scale = 1.2f;
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building enhanced Lightning Monkey...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Enhanced Lightning Monkey ready!
    echo ========================================
    echo.
    echo Enhancements:
    echo - Based on Wizard Monkey ^(better magical appearance^)
    echo - Enhanced range ^(+15 instead of +8^)
    echo - Faster attack rate ^(0.8s instead of 1.0s^)
    echo - More damage ^(3 instead of 1^)
    echo - Higher pierce ^(+3 instead of +1^)
    echo - Lightning bolt visual effects
    echo.
    echo Launch BTD6 to see your enhanced Lightning Monkey!
) else (
    echo Build failed. Creating simpler enhanced version...
    
    ^(
    echo using BTD_Mod_Helper.Api.Towers;
    echo using BTD_Mod_Helper.Extensions;
    echo using Il2CppAssets.Scripts.Models.Towers;
    echo using Il2CppAssets.Scripts.Models.TowerSets;
    echo.
    echo namespace LightningMonkey.Towers {
    echo   public class LightningMonkeyTower : ModTower {
    echo     public override string BaseTower =^> TowerType.WizardMonkey;
    echo     public override TowerSet TowerSet =^> TowerSet.Magic;
    echo.
    echo     public override string DisplayName =^> "Lightning Monkey";
    echo     public override string Description =^> "Channels crackling lightning bolts!";
    echo     public override int Cost =^> 550;
    echo.
    echo     public override int TopPathUpgrades =^> 5;
    echo     public override int MiddlePathUpgrades =^> 5;
    echo     public override int BottomPathUpgrades =^> 5;
    echo.
    echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
    echo       m.range += 15f;
    echo       var atk = m.GetAttackModel^(^);
    echo       atk.weapons[0].Rate = 0.8f;
    echo       var p = atk.weapons[0].projectile;
    echo       p.pierce += 3;
    echo       p.GetDamageModel^(^).damage = 3;
    echo       p.scale = 1.2f;
    echo     }
    echo   }
    echo }
    ^) ^> "%TOWER_FILE%"
    
    echo Building simpler enhanced version...
    dotnet build -c Release --nologo
    
    if %ERRORLEVEL% EQU 0 ^(
        echo SUCCESS! Enhanced Lightning Monkey ready!
        echo - Now uses Wizard Monkey as base ^(better appearance^)
        echo - Enhanced stats: +15 range, 3 damage, +3 pierce, faster rate
    ^) else ^(
        echo Build still failed. Check errors above.
    ^)
)
pause
