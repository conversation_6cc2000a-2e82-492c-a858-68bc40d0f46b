@echo off
echo Creating a clean Lightning Monkey tower file...

set "TOWER_FILE=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\LightningMonkey.cs"

echo Writing corrected tower file...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.DartMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "Channels crackling bolts that chain through bloons.";
echo     public override int Cost =^> 550;
echo.
echo     public override string Icon =^> "LightningMonkey-Icon";
echo     public override string Portrait =^> "LightningMonkey-Portrait";
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 8f;
echo       var atk = m.GetAttackModel^(^);
echo       var druid020 = m.GetTowerFromId^("Druid-020"^).GetAttackModel^(^);
echo       atk.weapons[0].projectile = druid020.weapons[0].projectile.Duplicate^(^);
echo       atk.weapons[0].Rate *= 0.95f;
echo       var p = atk.weapons[0].projectile;
echo       p.pierce += 1;
echo       p.GetDamageModel^(^).damage += 1;
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building final version...
cd /d "%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS! Lightning Monkey mod built and copied to Mods folder!
    echo Launch BTD6 to see your new Lightning Monkey in the Magic category.
) else (
    echo Build failed. Check errors above.
)
pause
