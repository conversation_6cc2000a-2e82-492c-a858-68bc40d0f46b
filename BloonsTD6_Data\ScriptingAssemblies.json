{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.AMDModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GraphicsStateCollectionSerializerModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HierarchyCoreModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputForUIModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.MarshallingModule.dll", "UnityEngine.MultiplayerModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.ShaderVariantAnalyticsModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp-firstpass.dll", "Assembly-CSharp.dll", "NativeShare.Runtime.dll", "Newtonsoft.Json.UnityConverters.Addressables.dll", "Newtonsoft.Json.UnityConverters.dll", "Newtonsoft.Json.UnityConverters.Mathematics.dll", "NinjaKiwi.Common.Attribution.dll", "NinjaKiwi.Common.dll", "NinjaKiwi.GUTS.dll", "Purchasing.Common.dll", "Steam.dll", "Twitch.dll", "Unity.2D.Common.Runtime.dll", "Unity.2D.SpriteShape.Runtime.dll", "Unity.Addressables.dll", "Unity.Burst.dll", "Unity.Collections.dll", "Unity.InputSystem.dll", "Unity.InputSystem.ForUI.dll", "Unity.InternalAPIEngineBridge.001.dll", "Unity.Mathematics.dll", "Unity.Multiplayer.Center.Common.dll", "Unity.Profiling.Core.dll", "Unity.Rendering.LightTransport.Runtime.dll", "Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.Shared.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "Unity.RenderPipelines.GPUDriven.Runtime.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Unity.RenderPipelines.Universal.2D.Runtime.dll", "Unity.RenderPipelines.Universal.Config.Runtime.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.RenderPipelines.Universal.Shaders.dll", "Unity.ResourceManager.dll", "Unity.ScriptableBuildPipeline.dll", "Unity.Services.CloudDiagnostics.dll", "Unity.Services.Core.Analytics.dll", "Unity.Services.Core.Components.dll", "Unity.Services.Core.Configuration.dll", "Unity.Services.Core.Device.dll", "Unity.Services.Core.dll", "Unity.Services.Core.Environments.dll", "Unity.Services.Core.Environments.Internal.dll", "Unity.Services.Core.Internal.dll", "Unity.Services.Core.Networking.dll", "Unity.Services.Core.Registration.dll", "Unity.Services.Core.Scheduler.dll", "Unity.Services.Core.Telemetry.dll", "Unity.Services.Core.Threading.dll", "Unity.TextMeshPro.dll", "UnityEngine.Purchasing.AppleCore.dll", "UnityEngine.Purchasing.AppleMacosStub.dll", "UnityEngine.Purchasing.AppleStub.dll", "UnityEngine.Purchasing.Codeless.dll", "UnityEngine.Purchasing.dll", "UnityEngine.Purchasing.SecurityCore.dll", "UnityEngine.Purchasing.SecurityStub.dll", "UnityEngine.Purchasing.Stores.dll", "UnityEngine.Purchasing.WinRTCore.dll", "UnityEngine.Purchasing.WinRTStub.dll", "UnityEngine.UI.dll", "UniWebView-CSharp.dll", "xcode_manipulation_api.editor.dll", "Unity.Collections.LowLevel.ILSupport.dll", "NCalc.dll", "NinjaKiwi.LiNK.dll", "Unity.Burst.Unsafe.dll", "DebugLog.dll", "NinjaKiwi.LiNK.Client.dll", "Newtonsoft.Json.dll", "RegEdit.dll", "Facepunch.Steamworks.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}