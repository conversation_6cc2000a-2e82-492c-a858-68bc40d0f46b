[loader]
# Disables <PERSON>on<PERSON>oa<PERSON>. Equivalent to the '--no-mods' launch option
disable = false
# Equivalent to the '--melonloader.debug' launch option
debug_mode = false
# Capture all Unity player logs into MelonLoader's logs even if the game disabled them. NOTE: Depending on the game or Unity version, these logs can be overly verbose. Equivalent to the '--melonloader.captureplayerlogs' launch option
capture_player_logs = false
# The maximum Harmony log verbosity to capture into MelonLoader's logs. Possible values in verbosity order are: "None", "Error", "Warn", "Info", "Debug", or "IL". Equivalent to the '--melonloader.harmonyloglevel' launch option
harmony_log_level = "Warn"
# Only use this if the game freezes when trying to quit. Equivalent to the '--quitfix' launch option
force_quit = false
# Disables the start screen. Equivalent to the '--melonloader.disablestartscreen' launch option
disable_start_screen = false
# Starts the dotnet debugger on Windows and wait it is attached or just wait until one is attached without launch on other OSes (only for Il2Cpp games). Equivalent to the '--melonloader.launchdebugger' launch option
launch_debugger = false
# Sets the loader theme. Currently, the only available themes are "Normal" and "Lemon". Equivalent to the '--melonloader.consolemode' launch option (0 for Normal, 4 for Lemon)
theme = "Normal"

[console]
# Hides warnings from displaying. Equivalent to the '--melonloader.hidewarnings' launch option
hide_warnings = false
# Hides the console. Equivalent to the '--melonloader.hideconsole' launch option
hide_console = false
# Forces the console to always stay on-top of all other applications. Equivalent to the '--melonloader.consoleontop' launch option
console_on_top = false
# Keeps the console title as original. Equivalent to the '--melonloader.consoledst' launch option
dont_set_title = false

[logs]
# Sets the maximum amount of log files in the Logs folder (Default: 10). Equivalent to the '--melonloader.maxlogs' launch option
max_logs = 10

[mono_debug_server]
# Let the Mono debug server wait until a debugger is attached when debug_mode is true (only for Mono games). Equivalent to the '--melonloader.debugsuspend' launch option
debug_suspend = false
# The IP address the Mono debug server will listen to when debug_mode is true (only for Mono games). Equivalent to the '--melonloader.debugipaddress' launch option
debug_ip_address = "127.0.0.1"
# The port the Mono debug server will listen to when debug_mode is true (only for Mono games). Equivalent to the '--melonloader.debugport' launch option
debug_port = 55555

[unityengine]
# Overrides the detected UnityEngine version. Equivalent to the '--melonloader.unityversion' launch option
version_override = ""
# Disables the console log cleaner (only applies to Il2Cpp games). Equivalent to the '--melonloader.disableunityclc' launch option
disable_console_log_cleaner = false
# A semicolon (;) separated list of paths that Mono will prioritise to seek mscorlib and core libraries before the Managed folder and Melon's included set of core libraries. Equivalent to the '--melonloader.monosearchpathoverride' launch option
mono_search_path_override = ""
# Forces the Il2Cpp Assembly Generator to run without contacting the remote API. Equivalent to the '--melonloader.agfoffline' launch option
force_offline_generation = false
# Forces the Il2Cpp Assembly Generator to use the specified regex. Equivalent to the '--melonloader.agfregex' launch option
force_generator_regex = ""
# Forces the Il2Cpp Assembly Generator to use the specified Il2Cpp dumper version. Equivalent to the '--melonloader.agfvdumper' launch option
force_il2cpp_dumper_version = ""
# Forces the Il2Cpp Assembly Generator to always regenerate assemblies. Equivalent to the '--melonloader.agfregenerate' launch option
force_regeneration = false
# Enables the CallAnalyzer processor for Cpp2IL. Equivalent to the '--cpp2il.callanalyzer' launch option
enable_cpp2il_call_analyzer = false
# Enables the NativeMethodDetector processor for Cpp2IL. Equivalent to the '--cpp2il.nativemethoddetector' launch option
enable_cpp2il_native_method_detector = false

