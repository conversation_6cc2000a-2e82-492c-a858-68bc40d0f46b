$ErrorActionPreference = 'Stop'
$csproj = Join-Path $env:USERPROFILE 'Documents/BTD6 Mod Sources/LightningMonkey/LightningMonkey.csproj'
if (-not (Test-Path $csproj)) { Write-Host "CSProj not found: $csproj"; exit 1 }

# Read raw text and trim anything before <Project
$raw = Get-Content -Raw -Encoding UTF8 $csproj
$idx = $raw.IndexOf('<Project')
if ($idx -gt 0) { $raw = $raw.Substring($idx) }

# Ensure the start tag ends with '>' and capture it
$match = [regex]::Match($raw, '<Project[^>]*>')
if (-not $match.Success) { Write-Host 'Malformed csproj: cannot find <Project ...> open tag'; exit 1 }
$openTag = $match.Value
$rest = $raw.Substring($match.Index + $match.Length)

# Ensure PropertyGroup for EmbeddedResource default disable exists
if ($raw -notmatch '<EnableDefaultEmbeddedResourceItems>') {
    $prop = "`n  <PropertyGroup>`n    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>`n  </PropertyGroup>`n"
    $raw = $openTag + $prop + $rest
}

# Basic sanity: ensure it ends with </Project>
if ($raw -notmatch '</Project>') {
    # Attempt to append a closing tag
    $raw = $raw.TrimEnd() + "`n</Project>`n"
}

# Write back
Set-Content -Path $csproj -Value $raw -Encoding UTF8
Write-Host 'Repaired csproj successfully.'

