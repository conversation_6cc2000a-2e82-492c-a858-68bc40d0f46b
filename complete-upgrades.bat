@echo off
echo Creating complete upgrade structure for Dark Lightning Monkey...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"

echo Removing old upgrades and creating complete structure...
if exist "%UPGRADES_DIR%" rmdir /s /q "%UPGRADES_DIR%"
mkdir "%UPGRADES_DIR%"

echo Creating TOP PATH upgrades (Dark Magic)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class DarkBoost : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 1;
echo     public override int Cost =^> 200;
echo     public override string DisplayName =^> "Dark Boost";
echo     public override string Description =^> "Lightning infused with dark energy. +1 damage, +2 pierce.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.pierce += 2f;
echo         projectile.GetDamageModel^(^).damage += 1f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\DarkBoost.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class ShadowStrike : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 2;
echo     public override int Cost =^> 400;
echo     public override string DisplayName =^> "Shadow Strike";
echo     public override string Description =^> "Dark lightning strikes faster. +1 damage, 15%% faster attacks.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.GetDamageModel^(^).damage += 1f;
echo         attackModel.weapons[0].Rate *= 0.85f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\ShadowStrike.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class VoidChanneling : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 3;
echo     public override int Cost =^> 1000;
echo     public override string DisplayName =^> "Void Channeling";
echo     public override string Description =^> "Channels void energy. +2 damage, +3 pierce, larger projectiles.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.GetDamageModel^(^).damage += 2f;
echo         projectile.pierce += 3f;
echo         projectile.scale *= 1.3f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\VoidChanneling.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class DarkMastery : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 4;
echo     public override int Cost =^> 8000;
echo     public override string DisplayName =^> "Dark Mastery";
echo     public override string Description =^> "Masters dark magic. +3 damage, +5 pierce, 25%% faster attacks.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.GetDamageModel^(^).damage += 3f;
echo         projectile.pierce += 5f;
echo         attackModel.weapons[0].Rate *= 0.75f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\DarkMastery.cs"

echo Creating MIDDLE PATH upgrades (Chain Lightning)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class QuickCast : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 1;
echo     public override int Cost =^> 150;
echo     public override string DisplayName =^> "Quick Cast";
echo     public override string Description =^> "Faster lightning casting. 15%% faster attacks.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         attackModel.weapons[0].Rate *= 0.85f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\QuickCast.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class SurgeCasting : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 2;
echo     public override int Cost =^> 300;
echo     public override string DisplayName =^> "Surge Casting";
echo     public override string Description =^> "Lightning surges faster. Another 20%% faster attacks.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         attackModel.weapons[0].Rate *= 0.8f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\SurgeCasting.cs"

echo Creating Chain Lightning upgrade (Middle T3)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class ChainLightning : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 3;
echo     public override int Cost =^> 1400;
echo     public override string DisplayName =^> "Chain Lightning";
echo     public override string Description =^> "Lightning chains between nearby bloons!";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         var chainProjectile = projectile.Duplicate^(^);
echo         chainProjectile.pierce = 3f;
echo         chainProjectile.GetDamageModel^(^).damage = 3f;
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo           "ChainLightning", chainProjectile, new SingleEmissionModel^("", null^), true, false, false^)^);
echo         projectile.pierce += 3f;
echo         projectile.GetDamageModel^(^).damage += 2f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\ChainLightning.cs"

echo Creating Void Storm Master upgrade (Top T5)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class VoidStormMaster : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 5;
echo     public override int Cost =^> 45000;
echo     public override string DisplayName =^> "Void Storm Master";
echo     public override string Description =^> "DARK MAGIC: Creates devastating black holes that warp bloons into the void!";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.pierce = 15f;
echo         projectile.GetDamageModel^(^).damage = 25f;
echo         projectile.scale = 2.0f;
echo         var blackHoleProjectile = projectile.Duplicate^(^);
echo         blackHoleProjectile.pierce = 999f;
echo         blackHoleProjectile.GetDamageModel^(^).damage = 50f;
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo           "BlackHole", blackHoleProjectile, new SingleEmissionModel^("", null^), true, false, false^)^);
echo         attackModel.weapons[0].Rate = 0.3f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\VoidStormMaster.cs"

echo Creating BOTTOM PATH upgrades (Range/Precision)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class ExtendedRange : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> BOTTOM;
echo     public override int Tier =^> 1;
echo     public override int Cost =^> 180;
echo     public override string DisplayName =^> "Extended Range";
echo     public override string Description =^> "Magical staff extends reach. +10 range.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       towerModel.range += 10f;
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\ExtendedRange.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class PrecisionStrike : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> BOTTOM;
echo     public override int Tier =^> 2;
echo     public override int Cost =^> 350;
echo     public override string DisplayName =^> "Precision Strike";
echo     public override string Description =^> "More precise lightning. +1 damage, +8 range.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       towerModel.range += 8f;
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.GetDamageModel^(^).damage += 1f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\PrecisionStrike.cs"

echo Building complete Dark Lightning Monkey...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Complete Dark Lightning Monkey!
    echo ========================================
    echo.
    echo COMPLETE UPGRADE PATHS:
    echo.
    echo TOP PATH ^(Dark Magic^):
    echo   T1: Dark Boost - +1 damage, +2 pierce ^(200^)
    echo   T2: Shadow Strike - +1 damage, 15%% faster ^(400^)
    echo   T3: Void Channeling - +2 damage, +3 pierce ^(1000^)
    echo   T4: Dark Mastery - +3 damage, +5 pierce, 25%% faster ^(8000^)
    echo   T5: Void Storm Master - BLACK HOLE MAGIC! ^(45000^)
    echo.
    echo MIDDLE PATH ^(Speed/Chain^):
    echo   T1: Quick Cast - 15%% faster ^(150^)
    echo   T2: Surge Casting - 20%% faster ^(300^)
    echo   T3: Chain Lightning - Lightning chains! ^(1400^)
    echo   T4: ^(Will add next^)
    echo   T5: ^(Will add next^)
    echo.
    echo BOTTOM PATH ^(Range^):
    echo   T1: Extended Range - +10 range ^(180^)
    echo   T2: Precision Strike - +1 damage, +8 range ^(350^)
    echo   T3-T5: ^(Will add next^)
    echo.
    echo Launch BTD6 to test your Dark Lightning Monkey!
    echo ^(Magic category, 750 cost^)
) else (
    echo Build failed. Check errors above.
)
pause
