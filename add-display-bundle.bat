@echo off
echo Adding custom display bundle support (attach <PERSON><PERSON>'s staff to brown monkey)...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "DISPLAY_DIR=%PROJ_DIR%\Displays"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

if not exist "%DISPLAY_DIR%" mkdir "%DISPLAY_DIR%"

REM 1) Create Display class that loads staff from AssetBundle 'lightning_staff' and prefab 'GeraldoStaff'
(
echo using BTD_Mod_Helper.Api.Display;
echo using BTD_Mod_Helper.Api;
echo using BTD_Mod_Helper;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Unity.Display;
echo using UnityEngine;
echo.
echo namespace LightningMonkey.Displays {
	echo   public class LightningMonkeyDisplay : ModTowerDisplay<LightningMonkey.Towers.LightningMonkeyTower> {
		echo     public override string BaseDisplay =^> GetDisplay(Il2CppAssets.Scripts.Models.TowerSets.TowerType.DartMonkey);
		echo     public override void ModifyDisplayNode(UnityDisplayNode node) {
			echo       // Try to load asset bundle named 'lightning_staff' with prefab 'GeraldoStaff'
			echo       try {
				echo         var bundle = ModContent.GetBundle("lightning_staff");
				echo         if ^(bundle != null^) {
					echo           var staffPrefab = bundle.LoadAsset^<GameObject^>("GeraldoStaff");
					echo           if ^(staffPrefab != null^) {
						echo             var hand = node.transform.Find("RHand") ?? node.transform.Find("RightHand") ?? node.transform.Find("weapon") ?? node.transform;
						echo             var staff = Object.Instantiate(staffPrefab, hand);
						echo             staff.transform.localPosition = new Vector3(0.05f, 0.02f, 0f);
						echo             staff.transform.localRotation = Quaternion.Euler(0f, 0f, -20f);
						echo             staff.transform.localScale = Vector3.one * 0.8f;
					echo           }
				echo         }
			echo       } catch ^(System.Exception^) { }
		echo     }
	echo   }
	echo }
) > "%DISPLAY_DIR%\LightningMonkeyDisplay.cs"

REM 2) Ensure tower applies the display
powershell -Command "(Get-Content -Raw '%TOWER_FILE%') -replace '\) \{\r?\n\s*// Slightly increase range for the staff', ') {\r\n      model.ApplyDisplay<LightningMonkey.Displays.LightningMonkeyDisplay>();\r\n      // Slightly increase range for the staff' | Set-Content '%TOWER_FILE%'"

REM 3) Create Assets folder for the bundle with a README
set "ASSETS_DIR=%PROJ_DIR%\Assets\lightning_staff"
if not exist "%ASSETS_DIR%" mkdir "%ASSETS_DIR%"
(
echo Place your Unity AssetBundle here named exactly: lightning_staff

echo Inside the bundle, include a prefab named: GeraldoStaff

echo The display loader will attach this prefab to the monkey's hand at runtime.

echo Recommended export: Built with Unity 2019.4.x AssetBundle, no materials compression; pivot at handle.
) > "%ASSETS_DIR%\README.txt"

cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

if %ERRORLEVEL% EQU 0 (
  echo ========================================
  echo SUCCESS! Custom display hook added.
  echo ========================================
  echo - Put your staff AssetBundle here: %ASSETS_DIR%
  echo - Bundle name: lightning_staff, Prefab: GeraldoStaff
  echo - The display attaches staff to right hand bone.
) else (
  echo Build failed. Fix compile errors above.
)
pause
