@echo off
echo Creating minimal working Dark Lightning Monkey...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"

echo Removing old files and creating minimal structure...
if exist "%UPGRADES_DIR%" rmdir /s /q "%UPGRADES_DIR%"
mkdir "%UPGRADES_DIR%"

echo Writing minimal tower with 0 upgrades...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.DartMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Dark Lightning Monkey";
echo     public override string Description =^> "A mystical monkey wielding cyan lightning staff! Shoots crackling lightning bolts and can warp reality with dark magic black holes.";
echo     public override int Cost =^> 750;
echo.
echo     // Set to 0 upgrades to avoid tier issues
echo     public override int TopPathUpgrades =^> 0;
echo     public override int MiddlePathUpgrades =^> 0;
echo     public override int BottomPathUpgrades =^> 0;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel towerModel^) {
echo       // Enhanced stats for lightning staff
echo       towerModel.range += 18f;
echo.
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var weaponModel = attackModel.weapons[0];
echo         weaponModel.Rate = 0.8f;
echo.
echo         var projectile = weaponModel.projectile;
echo         projectile.pierce = 5f;
echo         projectile.GetDamageModel^(^).damage = 4f;
echo         projectile.scale = 1.4f;
echo       }
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building minimal Dark Lightning Monkey...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Minimal Dark Lightning Monkey!
    echo ========================================
    echo.
    echo FEATURES:
    echo - Normal brown monkey appearance ^(like your image^)
    echo - Cyan lightning staff weapon
    echo - Enhanced range +18 ^(magical staff^)
    echo - 4 damage cyan lightning bolts
    echo - 5 pierce base
    echo - 1.4x larger projectiles
    echo - 0.8s attack rate
    echo.
    echo NO UPGRADES ^(to avoid tier conflicts^)
    echo - This is a working base tower
    echo - We can add upgrades once this works
    echo.
    echo Launch BTD6 to test! ^(Magic category, 750 cost^)
    echo If this works, we'll add the black hole upgrades!
) else (
    echo Build failed. Check errors above.
)
pause
