<linker>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AccoladesDisplayButton" preserve="all" />
    <type fullname="AnimationAudio" preserve="all" />
    <type fullname="AnimationEventAudio" preserve="all" />
    <type fullname="AnimatorRandomIdleSelector" preserve="all" />
    <type fullname="AnimatorRepeat" preserve="all" />
    <type fullname="Assets.Cursor" preserve="all" />
    <type fullname="Assets.MainMenuWorld.Scripts.MainMenuWorldChoreographer" preserve="all" />
    <type fullname="Assets.Scripts.Data.Accolades.AccoladeData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Accolades.AllAccolades" preserve="all" />
    <type fullname="Assets.Scripts.Data.Achievements.Achievement" preserve="all" />
    <type fullname="Assets.Scripts.Data.Achievements.AchievementData" preserve="all" />
    <type fullname="Assets.Scripts.Data.AnimatedCharacters.QuestCharacter" preserve="all" />
    <type fullname="Assets.Scripts.Data.AnimCurves.AnimCurve" preserve="all" />
    <type fullname="Assets.Scripts.Data.Artifacts.BoostArtifactData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Artifacts.ItemArtifactData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Artifacts.MapArtifactData" preserve="all" />
    <type fullname="Assets.Scripts.Data.ArtifactsData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Audio.AudioJukeBox" preserve="all" />
    <type fullname="Assets.Scripts.Data.Audio.MusicItem" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.Attack" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.AttackFilter" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.FollowTouchSetting" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.RotateToPointer" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.TargetClose" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.TargetFirst" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.TargetLast" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Attacks.TargetStrong" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Bloons.CreateEffectOnPop" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Display" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Effect" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Emissions.EmissionRotationOffTowerDirection" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Emissions.SingleEmission" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Events.EntityEvent" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.OffsetPosition" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.Age" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.Cash" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.CreateProp" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.CreateSoundOnPickup" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.CreateTextEffect" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.Damage" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.Pickup" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.Projectile" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Projectiles.TravelStrait" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Props.Prop" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Sound" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Towers.AddGravityToEntity" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Towers.AirUnit" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Towers.CreateEffectOnPlace" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Towers.CreateEffectOnSell" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Towers.CreateSoundOnBloonDestroyed" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Towers.HeliMovement" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Weapons.EjectEffect" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Weapons.FireFromAirUnit" preserve="all" />
    <type fullname="Assets.Scripts.Data.Behaviors.Weapons.Weapon" preserve="all" />
    <type fullname="Assets.Scripts.Data.Bloons.BloonImmuneSoundScriptable" preserve="all" />
    <type fullname="Assets.Scripts.Data.Bloons.BloonOverlayData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Bloons.BloonOverlayScriptable" preserve="all" />
    <type fullname="Assets.Scripts.Data.Boss.Bosses" preserve="all" />
    <type fullname="Assets.Scripts.Data.Boss.BossRushData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Boss.BossRushScriptable" preserve="all" />
    <type fullname="Assets.Scripts.Data.Boss.MenuBackgrounds" preserve="all" />
    <type fullname="Assets.Scripts.Data.BTD6AssetLibrary" preserve="all" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.ContestedTerritoryData" preserve="all" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.TeamsStoreItem" preserve="all" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.TeamsStoreItems" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.BloonAssetChanges.BloonAssetChange" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.BloonDecals.BloonDecal" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.BloonPopFXs.BloonPopFX" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.DarkTempleAssetChanges.DarkTempleAssetChange" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.Pets.Pet" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.PowerAssetChanges.PowerAssetChange" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.Props.Prop" preserve="all" />
    <type fullname="Assets.Scripts.Data.Cosmetics.TowerAssetChanges.TowerAssetChange" preserve="all" />
    <type fullname="Assets.Scripts.Data.CustomInGameHud.CustomInGameHuds" preserve="all" />
    <type fullname="Assets.Scripts.Data.DifficultySprites.DifficultySpritesAsset" preserve="all" />
    <type fullname="Assets.Scripts.Data.EmotesNS.Emote" preserve="all" />
    <type fullname="Assets.Scripts.Data.EmotesNS.Emotes" preserve="all" />
    <type fullname="Assets.Scripts.Data.Feats.LegendsFeatData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Feats.LegendsFeatsData" preserve="all" />
    <type fullname="Assets.Scripts.Data.GameData" preserve="all" />
    <type fullname="Assets.Scripts.Data.GameEditor.GameEditorData" preserve="all" />
    <type fullname="Assets.Scripts.Data.GameEditor.GraphNodeModelDescriptor" preserve="all" />
    <type fullname="Assets.Scripts.Data.Gameplay.FreeplayGroupData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Gameplay.IncomeSet" preserve="all" />
    <type fullname="Assets.Scripts.Data.Gameplay.Mods.Mod" preserve="all" />
    <type fullname="Assets.Scripts.Data.Gameplay.RoundSet" preserve="all" />
    <type fullname="Assets.Scripts.Data.Gameplay.TalkingHeadHint" preserve="all" />
    <type fullname="Assets.Scripts.Data.Gameplay.TalkingHeadHints" preserve="all" />
    <type fullname="Assets.Scripts.Data.Global.BuffIconSprites" preserve="all" />
    <type fullname="Assets.Scripts.Data.Global.LootIconSprites" preserve="all" />
    <type fullname="Assets.Scripts.Data.Global.RankInfo" preserve="all" />
    <type fullname="Assets.Scripts.Data.Global.TowerBackgroundSprites" preserve="all" />
    <type fullname="Assets.Scripts.Data.Input.GamepadData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Input.SelectableMeshConfig" preserve="all" />
    <type fullname="Assets.Scripts.Data.Input.SolidShadowConfig" preserve="all" />
    <type fullname="Assets.Scripts.Data.Knowledge.KnowledgeItem" preserve="all" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledgeData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Languages.DynamicFontReferences" preserve="all" />
    <type fullname="Assets.Scripts.Data.Languages.LanguageAsset" preserve="all" />
    <type fullname="Assets.Scripts.Data.Legends.RogueBloonModifierData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Legends.RogueData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Legends.RogueMapTemplate" preserve="all" />
    <type fullname="Assets.Scripts.Data.Legends.RogueXpShopItem" preserve="all" />
    <type fullname="Assets.Scripts.Data.MapEditor.MapEditorAreaTextureData" preserve="all" />
    <type fullname="Assets.Scripts.Data.MapEditor.MapEditorBaseMapTextureData" preserve="all" />
    <type fullname="Assets.Scripts.Data.MapEditor.MapEditorData" preserve="all" />
    <type fullname="Assets.Scripts.Data.MapEditor.MapEditorProp" preserve="all" />
    <type fullname="Assets.Scripts.Data.MapEditor.MapEditorStampData" preserve="all" />
    <type fullname="Assets.Scripts.Data.MapEditor.MapEditorTrackTextureData" preserve="all" />
    <type fullname="Assets.Scripts.Data.MapSets.MapSet" preserve="all" />
    <type fullname="Assets.Scripts.Data.ModeData.ModesData" preserve="all" />
    <type fullname="Assets.Scripts.Data.PowersProData" preserve="all" />
    <type fullname="Assets.Scripts.Data.ProfileAvatarsNS.ProfileAvatar" preserve="all" />
    <type fullname="Assets.Scripts.Data.ProfileAvatarsNS.ProfileAvatars" preserve="all" />
    <type fullname="Assets.Scripts.Data.ProfileAvatarsNS.ProfileBanner" preserve="all" />
    <type fullname="Assets.Scripts.Data.ProfileAvatarsNS.ProfileBanners" preserve="all" />
    <type fullname="Assets.Scripts.Data.Quests.QuestData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Quests.QuestDetails" preserve="all" />
    <type fullname="Assets.Scripts.Data.Quests.TaskData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Skins.SkinData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Skins.SkinsData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Store.LootThemes" preserve="all" />
    <type fullname="Assets.Scripts.Data.Store.StoreItem" preserve="all" />
    <type fullname="Assets.Scripts.Data.Store.StoreItems" preserve="all" />
    <type fullname="Assets.Scripts.Data.Sweepstakes.SweepstakesData" preserve="all" />
    <type fullname="Assets.Scripts.Data.Teams.CTTeamsBuildingMaterialSwap" preserve="all" />
    <type fullname="Assets.Scripts.Data.Teams.TeamsData" preserve="all" />
    <type fullname="Assets.Scripts.Data.TowerSnappingData" preserve="all" />
    <type fullname="Assets.Scripts.Data.TrackArrowData" preserve="all" />
    <type fullname="Assets.Scripts.Data.TrophyStore.TrophyStoreItem" preserve="all" />
    <type fullname="Assets.Scripts.Data.TrophyStore.TrophyStoreItems" preserve="all" />
    <type fullname="Assets.Scripts.GameEditor.UI.GameEditorView" preserve="all" />
    <type fullname="Assets.Scripts.Models.ServerEvents.DisableEventObjects" preserve="all" />
    <type fullname="Assets.Scripts.Models.ServerEvents.EnableEventObjects" preserve="all" />
    <type fullname="Assets.Scripts.Models.ServerEvents.SwapEventObject" preserve="all" />
    <type fullname="Assets.Scripts.Models.ServerEvents.SwapEventSprite" preserve="all" />
    <type fullname="Assets.Scripts.Models.ServerEvents.SwapEventTexture" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Audio.AudioFactory" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Audio.JukeBoxScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Audio.MiniJukeBoxPlayer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Cascade.Variable.FloatVariable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventCardItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventFeaturedInstaIcon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventFeaturedInstas" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventMilestoneItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventMysteryBox" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventRewardsContainer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventRewardsUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.CollectionEvent.CollectionEventUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.AnimationEventHandler" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.AnimationProperties" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.CustomMeshFrameAnimator" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.CustomSpriteFrameAnimator" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.EyesBlink" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.MonkeyAnimationController" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.PlayableAnimator" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.PlayableAnimatorSimple" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.PropAnimationController" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.SpeedUpSlowDownRotation" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.FaceCamera" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.LodGroup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.NodeLifespan" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.OffsetTowardsCamera" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.ProjectileTrailEffect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.QualityPurge" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.QualityPurgeStopAtTime" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Display.UnityDisplayNode" preserve="all" />
    <type fullname="Assets.Scripts.Unity.EditorBox" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Effects.Effect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Game" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.ActorComponentView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.BehaviorBrowserView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.BehaviorComponentView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.BehaviorLineItemView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.BehaviorListView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.EntityView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.EventSystem.ComponentEventSystemView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.GameEditorLibraryView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.LibraryGridItemView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.LibraryListItemView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.VariableDisplayView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.VariableImageView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.VariableInputfieldView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.VariableSwitchableScriptView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.GameEditor.UI.PopupPanels.VariableToggleView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.BTD6Gamepad" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.ConsoleShared" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.DropdownGamepadContext" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadAutoClickOnSelect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadAutoToggleOnSelect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadCenterScroll" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadContext" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadInactive" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadInputField" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadScaleSelected" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadScrollConstraint" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadSelectedState" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.GamepadSliderStep" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.InputModeActive" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.InputModeGraphicEnabled" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.Jewel.Jewel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.Jewel.JewelBar" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.Jewel.JewelBarButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.Jewel.JewelBarButtonSet" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.Jewel.JewelBarItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.Jewel.JewelBarToggle" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.Jewel.ToggleJewel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Gamepad.UIEffect.SelectableMesh" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.AbilityCooldownAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Actions.DarkDungeonsFireTriggerAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Actions.SetAreaActiveAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Actions.SetRegenRemovableActiveAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Actions.SuspendTowersOnAreaAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Actions.TriggerMapActionsAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.AnimatorRandomStartFrame" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.AnimatorRandomStatePlayLength" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.AnimCurve" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.ApplyTowerFreeze" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Area" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.BaseArea" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.BezierLineNodes" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.CamoDetectAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.CoopArea" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.CoopAreaDivisionType" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.CoopAreaLayout" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.CoopAreaMark" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.CurvedLineNodes" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.DamageAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.DamageTypeAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Effects.CreateObjectOnRemoveableSold" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Effects.HideObjectOnRemoveableSold" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Effects.InteractableClick" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Effects.InteractableClickSequence" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Effects.PlayAnimationOnRemoveableSold" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Effects.PlayEffectOnRemoveableSold" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Effects.PlaySoundOnRemoveableSold" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Extras.CandyFallsOompaChecker" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Gizmos.DarkDungeonsStatue" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Gizmos.GizmoTimeDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Gizmos.MapGizmo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Gizmos.WorkshopPowerCore" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Gizmos.WorkshopReverseMotor" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.HeroXpAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.LeakPoint" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.LinearRoundBasedCost" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Map" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.MapMods" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Path" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.PathPointInfo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.PierceAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.PolygonArea" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.PolyLineNodes" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.PriceAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.RandomStateBehavior" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.RangeAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.RateAreaBuff" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.RegenRemovable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Removeable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.ScrollTexture" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.AlternateRoundMultiPathSplitter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.AlternateRoundSplitter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.BloonTagOnlySplitter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.MoabOnlySplitter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.PathSpawner" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.RuleBossBloon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.RulePrimeNumberRound" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.RuleSplitter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Spawners.ToggleSplitter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Splitter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.AdjustShaderAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.AdoraSunGodDarkMapTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.AfterRoundTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.BloonsPoppedTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.ChangeEmitProjectileAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.ChangeSimTimeElapsedTriggerAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.CreateAreaAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.CreateAreasAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.CreateTowerAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.CycleAnimationsAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.CycleAreasAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.DarkDungeonsStatueActiveTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.DestroyAreaAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.DestroyAreasAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.EmitProjectileAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.EnableGameObjectAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.EncryptedTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.IncrementCaveMonkeyHitsAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.InteractableClickSequenceTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.InteractableClickTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.LivesLowerThanTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.MapActions" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.MapEvent" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.MapEventActiveAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.MovingPlatformAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.OnMapCreatedTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.OnMapLoadedWithActiveRegenRemovableTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.OnProjectileHitTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.PathActiveAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.PlayAnimationAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.PlayEffectAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.PlaySoundUnityAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.PreBloonSpawnTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.RemovableActiveAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.RemoveableSoldTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.RepeatableTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.ReverseModeTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.RotateAreaAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.ScrollTextureChangeAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.SellRemoveableAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.SimTimeElapsedTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.StartMapTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.StartRoundTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.ToggleSplitterAction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.TowerInAreaTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Map.Triggers.TowerPlacedTrigger" preserve="all" />
    <type fullname="Assets.Scripts.Unity.MapEditor.PowerEditorPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.MapEditor.TowerEditorPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.MapProps.Behaviors.AnimationSpecialEffect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.MapProps.Behaviors.DisableTowersInRange" preserve="all" />
    <type fullname="Assets.Scripts.Unity.MapProps.MapProp" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Menu.MenuManager" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Powers.Effects.CreateEffectOnPower" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Powers.Power" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Render.MPB_LerpFloatOnEnable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Scenes.DataConflictScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Scenes.InitialLoadingScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Scenes.LoadingScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Scenes.NinjaKiwiSplashScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Scenes.TitleScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Attack" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Behaviors.AttackFilter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Behaviors.RotateToTarget" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Behaviors.SheRaTarget" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Behaviors.TargetClose" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Behaviors.TargetFirst" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Behaviors.TargetLast" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.Attack.Behaviors.TargetStrong" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CircleFootprint" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CreateEffectOnPlace" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CreateEffectOnSell" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CreateEffectOnTowerExpire" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CreateSoundOnSell" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CreateSoundOnTowerExpire" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CreateSoundOnTowerPlace" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.CreditPopsToParentTower" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.IgnoreAllMutatorsTower" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.PlayAnimationIndex" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.SavedSubTower" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Behaviors.TowerExpire" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.ClickBox" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Emissions.InstantDamageEmission" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Emissions.LineProjectileEmission" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Emissions.SingleEmission" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Filters.FilterAll" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Filters.FilterAllExceptTarget" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Filters.FilterInvisible" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Filters.FilterMoab" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Mods.ApplyMod" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.OrderedPrefabReferences" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.Age" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.ClearHitBloons" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.CreateEffectOnExpire" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.CreateProjectileOnContact" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.CreateProjectileOnExpire" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.CreateSoundOnProjectileCollision" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.CreateSoundOnProjectileCreated" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.CreateSoundOnProjectileExpire" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.Damage" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.DamageModifierForBloonType" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.DamageModifierForRound" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.Instant" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.ProjectileFilter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.RefreshPierce" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.Slow" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.SlowModifierForTag" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.TrackTarget" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Behaviors.TravelStrait" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Projectiles.Projectile" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.SubTower" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Tower" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Weapons.Behaviors.CreateEffectOnContact" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Weapons.Behaviors.CreateEffectProjectileAfterTime" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Weapons.Behaviors.EjectEffect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Towers.Weapons.Weapon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccessibilitySettingsUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccoladeDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccoladeNotification" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccoladeNotificationButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccoladesNotificationPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccoladeStatsDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccoladeSummaryScreenDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AccoladeSummaryScreenIcon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Achievements.AchievementPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Achievements.AchievementsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.AssetLibrary.AssetLibraryItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.BossBloons.BossLeaderboardScorePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.BossBloons.BossLeaderboardScoresGroup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.BossBloons.BossRushEventEndPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.BossBloons.BossRushPlayerAttack" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ButtonLite" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ButtonWithPip" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Callouts.CalloutUiData" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeBrowserDescriptionValidator" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeBrowserPanelPlayButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeBrowserSearchPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeBrowserTitleValidator" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeEditor" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeEditorModifierSettings" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeEditorPlay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeEditorSliderPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeEditorSliderSettings" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeRulesScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeStatPlayerInfo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ChallengeStatView" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ContentBrowser" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.CreationBrowser" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.CreationPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.CurationBrowser" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.CurationMapInfoPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.CurationVoteInfo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ExtraSettingsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ExtraSettingsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.GameCurationPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.GamePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.MapCurationPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.MapPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.MapRejectPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.MapReportPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.MapValidatePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ModifierSettingsButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.OdysseyPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.PlayerContentInfoPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.PlayerContentStatsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.ReportButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ChallengeEditor.TowerContainer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.CodeRedemptionPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.CollapsiblePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.CommonBackgroundScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.CommonForegroundScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.CommonPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CameraRig.ContestedTerritoryCameraRig" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.ContestedTerritoryMapParent" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.ContestedTerritoryMapScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.ContestedTerritoryTileDetailsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.ContestedTerritoryTilePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.ContestedTerritoryWorld" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTBaseIsland" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTDailyRewardPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTDailyRewardTrophyPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTEventEndScorePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTMapScorePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CtOneTeamBattleStateBanner" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTRelicManagerPopupScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTRelicSelectable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTRelicSelectableRulesMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CTTeamPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CtTeamScorePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CtThreeTeamBattleStateBanner" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.CtTwoTeamBattleStateBanner" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.MonoScripts.ContestedTerritoryTileAnimationLayer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.MonoScripts.ContestedTerritoryTileCapturedGroup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.MonoScripts.ContestedTerritoryTileHighlightLayer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.MonoScripts.ContestedTerritoryTileMaterialLayer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.TeamEventTitleScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ContestedTerritory.TileInteractable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopConfirmFreeplayPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopDifficuiltyMedalPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopInGamePlayerPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopJoinMatchScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopLobbyScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopLobbySearchField" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopMessageBoardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopNotificationPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopPlayerPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopPlayerPanelFreeplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopPlayerRibbonPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopPlayerSliderPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopPlayerStatsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopQuickMatchScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopReconnectingPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopSyncingPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.CoopVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.LocalCoopPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.PlaySocialScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Coop.UiCoopManager" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.CoopAreaVisualUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossEventCoopScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossEventScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossEventScreenPlayPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossEventScreenTierRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushMapScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushNewScoreInfo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushPlayScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushRelicDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushScoreDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushStageCompletePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushStagePanelDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushTileSelectionPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushWorld" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossRushWorldAudio" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossSelectButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.BossSelectPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DailyChallengeScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCModPowerDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCModTowerDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCModTowerDisplayRestriction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCModTowerPathRestriction" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCParagonRestrictionDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCRestrictionUpgradeDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCTowerDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.DCTowerRulesPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.MapSelectPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.RaceEventScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.RogueCampaignSummaryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.RogueCampfirePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.RogueMapScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DailyChallenge.RogueMentorPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DebugBloonCountItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DebugCollapsableOutputPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DebugCollapsableTowerOutputPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DebugMenus" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.DescriptionPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.EffectScaleControl" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.EndGame.BloonCauseDeathDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.EndGame.OdysseyVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Feats.FeatPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Feats.FeatsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameEvents.DailyChallengePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameEvents.GameEventsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameEvents.GenericEventPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.BossDefeatScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.BossRushVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.BossVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.ContestedTerritoryVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.CoopDefeatScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.CTDefeatScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.DefeatScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.LeaderboardVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.QuestVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.RaceVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.ReviewMapScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.RogueDefeatScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.RogueVictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.SinglePlayerStatsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.GameOver.VictoryScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.HeroInGame.HeroInGameScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.HoverableInfoPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.AbilitiesMenu.AbilityMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.ActionMenu.GoFastForwardToggle" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BloonMenu.BloonMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BloonMenu.BloonMenuToggle" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BloonMenu.SpawnBloonButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BossBloons.BossArmourUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BossBloons.BossTierRewardScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BossBloons.RushUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BuffIcon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.BuffIndicatorUi" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.CollectionBarHud" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.CoopAreaIndicators" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.CTBossHud" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.CTRaceHud" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.EditorMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.EditorMenuPopout" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.EditorRemoveablesLayerSelectPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.GameEditorScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorArea" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorDebugPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorMusicSelectPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorPath" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorPathSplitterPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorPathSplitterPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorSceneController" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.MapEditorSplitterPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.PathSelectNodes" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.PathSplineNode" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.PipPagingPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.RemoveableLayerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.SelectedDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.SplineNode" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.StampDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.StartEndNode" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.TangentNode" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EmotesMenu.Emotes.EmoteHeroData" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EmotesMenu.Emotes.EmotePlayerUi" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EmotesMenu.EmotesManager" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.HudController" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.InGame" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.LayerButtonProxy" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.LeastCashUsedHud" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.LeastTiersUsedHud" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.MapEditorCategoryLayersViewer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.MapPropsMenu.PropSelectionMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.MapPropsMenu.PropSelectionMenuTag" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.MapPropsMenu.PropSelectionMenuThemeManager" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.PropPackItemDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.PropPackPurchaseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.PropPackPurchasePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.PropsMenu.PropSelectionMenuThemes.TSMThemeDefaultProp" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.QuestRaceHud" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Races.BossRushUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Races.BossUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Races.RaceUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Removables.RemovablePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.Powers.InstaTowerGroupMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.Powers.InstaTowersMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.Powers.PowersMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.Powers.RoguePowerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.Powers.StandardInstaTowerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.Powers.StandardInstaTowerGroupButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.Powers.StandardPowerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.RightMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RightMenu.ShopMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RogueHudUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.RogueLeastCashHud" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StatBar" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.BossRushLivesDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.CashDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.CorvusSoulBarrierDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.GameMessageManager" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.HealthDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.HelperMessage" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.LoanDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.MaxMonkeysHelperMessage" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.MonkeyMoney" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.RoundDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.Stats.ShieldDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.AreaLayerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.CancelPurchaseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.ConfirmPurchaseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.PathLayerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.PropLayerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.RemoveableAreaLayerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.SpecialPropLayerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StampLayerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StandardAreaTextureButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StandardEditorPowerPurchaseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StandardEditorTowerPurchaseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StandardMapTextureButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StandardPropPurchaseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StandardStampPurchaseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.StandardTrackTextureButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.TabToggle" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.TowerPurchaseButton2D" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.TowerPurchaseButton3D" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.StoreMenu.TowerPurchaseButtonRogue" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.AbilityButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.CorvusSpellbookSpellUi" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.CorvusSpellbookUi" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.CurrentUpgrade" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.GeraldoShopMenuItemUi" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.GeraldoShopMenuUi" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.PowerProCurrentUpgrade" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.PowerProUpgradeButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.PowerProUpgradeObject" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.PowersProTierObject" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.StackedAbilityButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TierObject" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemeManager" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeAmbidextrousRangs" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeBananaFarm" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeBananaFarmer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeBeastHandler" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeCamo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeCorvus" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeDefault" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeDesperado" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeEnergisingTotem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeGeraldo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeMagusPerfectus" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeMonkeyAce" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeMonkeySub" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeRosalia" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeSelectInputOrDefault" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeSmallUpgradeable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeSuperMonkey" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeSuperMonkeyBeacon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeUnpoppedArmy" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.TowerSelectionMenuThemes.TSMThemeWithActionButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.UpgradeButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.UpgradeInfoPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.TowerSelectionMenu.UpgradeObject" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGameObjects" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Knowledge.KnowledgeMain" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Knowledge.KnowledgeSkillBtn" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Knowledge.KnowledgeSkillTree" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LeaderboardPlayerPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LeaderboardRewardsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LeaderboardRewardsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LeaderboardScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LeaderboardScreenScroll" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LeaderboardScreenToggle" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.DisplayArtifactsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.LegendsSelectPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueCurseInfoPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueEnvironment" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueLootChoiceButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueLootDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueLootDisplayButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueMap" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueMapCameraRig" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueMerchantPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueMonkeyMovement" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueTile" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.RogueTileInfoPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.SelectArtifactsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.UpgradeArtifactsPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LegendsMainMenuScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LevelUp.LevelUpKnowledgeScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LevelUp.LevelUpMonkeyMoneyScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LevelUp.LevelUpScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LevelUp.TowerUnlockScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LevelUp.UnlockTowerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Lightbox" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LinkFriendPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LinkFriendScoresPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LinkFriendsScoresPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LinkFriendsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LiNKGuildMemberPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LiNKGuildMemberPanelScore" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LiNKGuildMessageBossRushEventPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LiNKGuildMessageCtEventPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LiNKGuildMessageEventPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.LiNKSocial.LiNKGuildPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Mailbox.GiftLetter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Mailbox.MailboxLetter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Mailbox.MailboxScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Mailbox.MailLetterPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Mailbox.MailLetterPopupWithLoot" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Mailbox.SweepstakesWinnerPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.DailyRewards.DailyRewardItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.DailyRewards.DailyRewardsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.DailyRewards.TowerGiftBoxScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.DifficultySelect.DifficultySelectScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.EventPanel.MainMenuEventPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.EventPanel.MainMenuMaintenancePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.Facebook.FriendLoginButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.CommonForegroundExtraSettings" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.CommonForegroundScreenHeroButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.GiftboxUnlockSplash" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.HeroButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.HeroPurchaseSplash" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.HeroSkinButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.HeroSkinPurchaseDetails" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.HeroSkinPurchaseSplash" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.HeroUpgradeButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.HeroUpgradeDetails" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.NewHeroScreenNotification" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.HeroSelect.StorePurchaseAnimationUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.Home.CoopButtonChecker" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.Home.HomeAchievementsButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.Home.HomeFeatsButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.Home.KnowledgeEnabledChecker" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.Home.PipEventChecker" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.Mailbox.MailboxButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MainMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.CoopMapButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.MapButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.MapDifficultyLockFillBar" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.MapPip" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.MapSelectScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.MonkeyTeamsIcon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.NewMapChecker" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MapSelect.SeenMapChecker" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.ModeSelect.ModeButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.ModeSelect.ModeScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MonkeySelect.MonkeyButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MonkeySelect.MonkeyGroupButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MonkeySelect.MonkeySelectMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.MonkeySelect.NewTowerUnlockPip" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.PlayerInfo.PlayerInfo" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.PowersSelect.InstaTowerDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.PowersSelect.InstaTowerScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.PowersSelect.InstaTowerTypeDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.PowersSelect.PowerSelectButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.PowersSelect.PowersSelectScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.UpdateScreen.UpdateAnnouncementScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.InteractionChecker" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.OpenChest" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.OpenGameEventsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.OpenGiftbox" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.PlayInteractableAudio" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.PlayInteractableClip" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.PlayInteractableEffect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.QuestMonkey" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.WorldItems.QuestMonkeyGroup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.MainHudLeftAlign" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.MainHudRightAlign" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.MapInfoPane" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Nexus.NexusCodeInput" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Nexus.NexusSettingsButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.NexusPlayerCreatorPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyBoatInteractable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyBoatLoadoutEditor" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyBoatLoadoutPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyBoatSlot" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyChallengeEditor" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyCompletedPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyCrewDetailsPanelPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyEditor" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyEditorMap" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyEventScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyExtendedLootPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyInGameRulesScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyIntroPanelPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyIslandInfoPanelPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyIslandInteractable" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyMapPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyPowerDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyTowerDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyWorldMenu" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Pause.PauseScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PauseButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayerStats.AvatarItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayerStats.BannerItem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayerStats.Btd6PlayerStatPair" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayerStats.NamedMonkeyPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayerStats.OnMouseOverMessage" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayerStats.PlayerStatsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayerStats.TowerStatIcon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.PlayMovieClip" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.AccoladePurchaseIcon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.AccoladesStorePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.ArcadeDataConsentPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.AssetLibraryPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.BossEndedPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.CoopDisconnectedPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.CoopInvitePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.CTEndedPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.AgeConfirmPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.AgreePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.ConfirmNamePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.DeleteAccountPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.EpicAccountPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.LinkingCodePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.LoggedInPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Epic.MainAccountPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.GiftEventPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.HintPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.LeaderboardEventEndPopupPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.LogoutPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.MapDifficultyLockPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.MapDifficultyUnlockPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.ModdingPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.Popup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.PopupScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.RaceEndedPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.RacePassStorePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.RogueArtifactPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.RogueLootPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.RogueRewardPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.StorePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Popups.UnlockMapEditorPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.QuestDialogueSystems.QuestDialogueSystem" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.QuestBrowserScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.QuestPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.QuestPartButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.QuestScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.QuestSingleEventScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.RendererToUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.TaskPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Races.LeaderboardRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.CollectionRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.EventRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.GenericRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.HeroRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.InstaMonkeyRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.InstaRankRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.PowerRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.RewardsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Rewards.TrophyStoreRewardPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.RogueCurseInfoItemDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.RogueCurseItemDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.RogueCurseSelectPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.RogueMainMenuScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.RogueNewGameScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.RogueXpShopItemDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.RogueXpShopPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.AccessibleRangeCircle" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.AccessibleRangeCircles" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.AmbientMapEffects" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.HotkeysScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.HotkeysScreenField" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.LanguageSelectButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.LanguageSelectScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.ScreenSizeDropDown" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.ScreenSizeDropDownSelection" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.SettingsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.SliderPercentText" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Settings.TwitchSettingsUI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.ShaderUI.OdysseyShaderController" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.SlideshowInfoPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.SocialShareImage" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.StereoScaleControl" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.LegendsProductButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.LootItem.BaseLootItemPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.LootItem.BonusLootItemPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.LootItem.InstaTowerLootItemPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.LootPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.ModIconTheme" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.ProductButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.Purchaser" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.StoreScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Store.TowerProductButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.SweepStakes.SweepstakesEventEndedScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.SweepStakes.SweepStakesEventScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.SweepStakes.SweepstakesFirstTimePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.SweepStakes.SweepstakesReferralCodePopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.SweepStakes.SweepstakesWinnerDisplay" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.CompetitiveModePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.FriendsDebugPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamBrowserAdvancedPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsBrowserScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsCosmeticDebugPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsCosmeticDisplayPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsCreatePanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsDebugPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsItemUnlockPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsStoreScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsStoreUiDailyPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TeamsStoreUiPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Teams.TrophySpendPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.TmpLinkHandler" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Transitions.MapSelectTransition" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.TrophyStore.TrophyItemPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.TrophyStore.TrophyStoreFilterButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.TrophyStore.TrophyStoreScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Tutorial.InGameTutorial" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Tutorial.TutorialScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Twitch.TwitchItemStatusIcon" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Twitch.TwitchPollCreationPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Twitch.TwitchPollPanel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Twitch.TwitchPollPanelOption" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Twitch.TwitchTowerButton" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.UI" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Upgrade.SelectedUpgrade" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Upgrade.UpgradeDetails" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Upgrade.UpgradeScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.AnimationStateEvents" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.ButtonWithSyncingState" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.EnableIfBadActor" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.InGameMapRect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.InGameUIRect" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.InputFieldPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.ParagonConfirmationPopup" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.RatioCanvasScaler" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.RatioEnabler" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.RegisterCameraWithCommonBackgroundScreen" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.RegisterCameraWithStereoscope" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.SafeArea" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.StereoscopeOverrides" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.ToggleWithSyncingState" preserve="all" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.UIDragHandler" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.CustomDcSliderWithDeadzone" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.DynamicFontReplacer" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.GameObjectEvents" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.InvertCanvasScale" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.Scrollable3DElement" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.ScrollableElement" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.ScrollableSlider" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.Scroller3D" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.ScrollRectWithDragCancel" preserve="all" />
    <type fullname="Assets.Scripts.Unity.Utils.SharedMaterialFloatAccessor" preserve="all" />
    <type fullname="Assets.Scripts.Unity.VisionOS.BoundedInputManager" preserve="all" />
    <type fullname="Assets.Scripts.Unity.VisionOS.BoundedObjectBehavior" preserve="all" />
    <type fullname="Assets.Scripts.Unity.VisionOS.CullCanvas" preserve="all" />
    <type fullname="Assets.Scripts.Unity.VisionOS.ObjectVolumeLimiter" preserve="all" />
    <type fullname="Assets.Scripts.Unity.VisionOS.VisionOSFlippedImage" preserve="all" />
    <type fullname="Assets.Scripts.Unity.VisionOS.VisionOSInputHelper" preserve="all" />
    <type fullname="Assets.Scripts.Unity.VisionOS.VisionOSObjectSwap" preserve="all" />
    <type fullname="Assets.Scripts.Utils.OnlineProfileUpdater" preserve="all" />
    <type fullname="Assets.Scripts.Utils.RatioFovSetter" preserve="all" />
    <type fullname="Assets.Scripts.Utils.SteamUpdate" preserve="all" />
    <type fullname="Assets.Scripts.Utils.TurnOffComponentOnRelease" preserve="all" />
    <type fullname="Assets.Scripts.Utils.TurnOffIfReplaysNotEnabled" preserve="all" />
    <type fullname="Assets.Scripts.Utils.TurnOffOnPlatform" preserve="all" />
    <type fullname="Assets.Scripts.Utils.TurnOffOnRelease" preserve="all" />
    <type fullname="AudioPreviewModule" preserve="all" />
    <type fullname="BossRushTile" preserve="all" />
    <type fullname="ChallengeEditorModifierIcon" preserve="all" />
    <type fullname="ClockTimeAnimator" preserve="all" />
    <type fullname="ContinueGamePanel" preserve="all" />
    <type fullname="CustomColorAnimator" preserve="all" />
    <type fullname="CustomFlyoverAnimator" preserve="all" />
    <type fullname="CustomMoveAnimator" preserve="all" />
    <type fullname="CustomRendererToggleAnimator" preserve="all" />
    <type fullname="CustomRotationAnimator" preserve="all" />
    <type fullname="CustomRotationSimple" preserve="all" />
    <type fullname="CustomScaleAnimator" preserve="all" />
    <type fullname="DanceFloor" preserve="all" />
    <type fullname="DestroyComponent" preserve="all" />
    <type fullname="DifficultyCollectionEventItemLabel" preserve="all" />
    <type fullname="DifficultyCollectionEventItems" preserve="all" />
    <type fullname="DifficultySelectMmItems" preserve="all" />
    <type fullname="DigitalClockFaceAnimator" preserve="all" />
    <type fullname="EditorTrackItem" preserve="all" />
    <type fullname="FXVolumeControl" preserve="all" />
    <type fullname="GameObjectEnabler" preserve="all" />
    <type fullname="GestureSystem.TouchableObject" preserve="all" />
    <type fullname="IncludeMeshInBake" preserve="all" />
    <type fullname="IntegerInputFieldLimit" preserve="all" />
    <type fullname="LegendsStatUI" preserve="all" />
    <type fullname="LegendsUIAudio" preserve="all" />
    <type fullname="MapSelectDisplay" preserve="all" />
    <type fullname="ModeCollectionEventItemLabel" preserve="all" />
    <type fullname="ModeCollectionEventItems" preserve="all" />
    <type fullname="ModeSelectMonkeyMoneyLabel" preserve="all" />
    <type fullname="MusicVolumeControl" preserve="all" />
    <type fullname="NestedPrefab" preserve="all" />
    <type fullname="NK_TextMeshProInputField" preserve="all" />
    <type fullname="OnClickHandler" preserve="all" />
    <type fullname="PetSoundToggleControl" preserve="all" />
    <type fullname="PlayAnimationOnClick" preserve="all" />
    <type fullname="PlayAnimationToEnd" preserve="all" />
    <type fullname="PlayEffectOnClick" preserve="all" />
    <type fullname="PlaySoundOnDrag" preserve="all" />
    <type fullname="PlaySoundOnMapInteractableClick" preserve="all" />
    <type fullname="PowerImageLoader" preserve="all" />
    <type fullname="RenderMultipleDialogueCameraViews" preserve="all" />
    <type fullname="RogueArtifactDisplayIcon" preserve="all" />
    <type fullname="RogueCampWorld" preserve="all" />
    <type fullname="RogueExtractionPanel" preserve="all" />
    <type fullname="RogueGameModeRules" preserve="all" />
    <type fullname="RogueMiniGameScoreDisplay" preserve="all" />
    <type fullname="RoguePartyPanel" preserve="all" />
    <type fullname="RotateDisplayModel" preserve="all" />
    <type fullname="RotateModelAction" preserve="all" />
    <type fullname="RotateObjectOnClick" preserve="all" />
    <type fullname="SetActiveCallback" preserve="all" />
    <type fullname="SetOpenAnimationDuringRound" preserve="all" />
    <type fullname="SpinUpDisplayModel" preserve="all" />
    <type fullname="SweepstakesDailyTask" preserve="all" />
    <type fullname="SweepstakesDailyTaskActiveIcon" preserve="all" />
    <type fullname="SweepstakesDailyTaskTier" preserve="all" />
    <type fullname="ToggleImageSwap" preserve="all" />
    <type fullname="TowerContainerSpritesAsset" preserve="all" />
    <type fullname="TowerImageLoader" preserve="all" />
    <type fullname="TowerUnlockSelectionPanel" preserve="all" />
    <type fullname="TrackItem" preserve="all" />
    <type fullname="TwitchSettingsButton" preserve="all" />
    <type fullname="Unity.Utils.AddressableVideoPlayer" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.NonDrawingGraphic" preserve="all" />
    <type fullname="UpgradePopup" preserve="all" />
    <type fullname="WebviewBackground" preserve="all" />
    <type fullname="Assets.Scripts.Simulation.SMath.Circle" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Simulation.SMath.Vector3" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Map.PolygonArea/PointArray" preserve="nothing" serialized="true" />
    <type fullname="RangeValue" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.AnimationBakerStateConfig" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.AnimationBakerVariantStateConfig" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.AnimCurves.AnimCurveContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.AnimCurves.AnimCurveData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.BTD6AssetFlags" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Bloons.BloonImmuneSoundScriptable/BloonImmuneSound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Boss.BossData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Boss.BossDetailsContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Boss.BossType" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Boss.OverrideBackgroundContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Boss.OverrideScreenBackground" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Boss.ScreenBackground" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Boss.ScreenBackgroundContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.DecorativeTile" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.DecorativeTiles" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.ExtraAssets" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.IslandTheme" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.MapGeneration" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.MapSizing" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.PlaceableAsset" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.TeamDetail" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.TeamsStoreFlairData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ContestedTerritory.TileData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Cosmetics.AnimationSwap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Cosmetics.AudioSwap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Cosmetics.BloonDecals.BloonDecalSwap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Cosmetics.PrefabArraySwap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Cosmetics.PrefabSwap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Cosmetics.Props.Prop/OffsetDictionary" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Cosmetics.SpriteSwap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Gameplay.BloonGroup" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Gameplay.FreeplayGroupDataItem" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Gameplay.IncomeSet/Container" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Gameplay.RoundData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Global.BuffIconSprite" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Global.Rank" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Global.SpriteRefList" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Input.GamepadInputType" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Abilitized" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.AirAndSea" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.AlchemistTouch" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.BiggerBloonSabotage" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.BoxOfChocolates" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.BoxOfMonkey" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.BrokenHeart" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.CamoTrap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Camoflogged" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.DeepHeat" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.DurableShots" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.ElDorado" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.ExtraEmpowered" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.FlintTips" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Fortifried" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.GlueTrap" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.GoingTheDistance" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.HardBaked" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Heartless" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.HeroBoost" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.ManaBulwark" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.MarchingBoots" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.MoabClash" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.MoabMine" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.MonkeyBoost" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.MonkeyShieldMarkOne" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.MonkeyShieldMarkThree" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.MonkeyShieldMarkTwo" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.OpenSeason" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.PsiVision" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Regeneration" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Restoration" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.RoadSpikes" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.RoundingUp" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.RoyalTreatment" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Sharpsplosion" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.StartingStash" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.StoreOnlyMagicMonkeys" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.StoreOnlyMilitaryMonkeys" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.StoreOnlyMonkeyTycoon" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.StoreOnlyPrimaryPrimates" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.StoreOnlySupportSimians" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.SuperMonkeyStorm" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.TechBot" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Knowledge.RelicKnowledge.Thrive" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Languages.DynamicFontReferences/FontReferenceDict" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.ArtifactLimitXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.BanMapDifficultyXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.BanSellingXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.BloonSpeedXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.BoostDiscountXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.BoostRerollDiscountXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.DisableBoostsXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.DisableBronzeGoalXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.DisableHeroesXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.DisableRerollsXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.DisableStartingArtifactsXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.DisableXpShopXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.FinalBossHpXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.HeroXpXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.InstaCooldownXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.LegendaryTilesXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.LivesXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MapHeartsXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MaxLivesXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MerchantLegendaryChanceXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MerchantPricesXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MerchantRareChanceXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MiniBossScoreRequirementXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MiniGameScoreRequirementXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.MuatorModXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.PartySizeXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RecruitRerollsXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RewardChoiceAmountXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RogueFinalBossInfo" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RogueHeroStarterKit" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RogueInstaMonkey" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RoguePropGroup" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RogueTileAssetData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.RogueTileSaveData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.StartingArtifactsXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.StartingCashXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.TokenXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.TowerCashScaleXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.UpgradeDiscountXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Legends.UpgradeIncreaseXpShopMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.MapSets.MapDetails" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.MapSets.MapDetailsContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ModeData.ModeData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.ModeData.ModesData/ModeDataContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.DialogueContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.DialogueData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.DialogueDataQuest" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.DialogueDataTaskContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.InGame.DialogueInGameIntroduction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.InGame.DialogueInGameOnBloonPop" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.InGame.DialogueInGameOnBloonSpawned" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.InGame.DialogueInGameOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.InGame.DialogueInGameOnTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.InGame.DialogueInGameTowerUpgradedToTier" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.Legends.RogueQuestInfo" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestBehaviors.DialogueDisableableAfterVictoryQuest" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestBehaviors.HeroTrialQuest" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestBehaviors.ShowStoreIapButton" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestBehaviors.SpecialEventLootSetRewardableQuest" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestBehaviors.SpotlightedQuest" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestDialogueAnimData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestPartData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.QuestPartDataContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.TaskBehaviorContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.TaskBehaviors.ChallengeGoal" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.TaskBehaviors.RetryLastRoundAllowedBehavior" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.TaskBehaviors.RogueGoal" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.TaskBehaviors.SpawnBossEvent" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.TowerQuestContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.TutorialQuestData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.UI.DialogueFullQuestCompleted" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.UI.DialogueShowOnNextUiMenu" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.UI.DialogueTaskCompleted" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.UI.DialogueTaskIntroduction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Quests.UI.DialogueTaskOnDefeat" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.PortraitContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SkinContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.StorePortraits" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapAudio" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapAudioContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapOverlay" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapOverlayContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapPrefab" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapPrefabContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapProjectileSpriteGroup" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapProjectileSpriteGroupContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapShopPortrait" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapShopPortraitContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapShopProjectilePrefab" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapShopProjectilePrefabContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapShopSprite" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapShopSpriteContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapSprite" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Skins.SwapSpriteContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Store.LootThemeItem" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Store.StoreItem/Epic" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Sweepstakes.SweepstakesData/SweepstakesDataItem" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Sweepstakes.SweepstakesData/SweepstakesDetailsContainer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.TrophyStore.TrophyItemTypeData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.TrophyStore.TrophyStoreTypeIcon" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AbilityStackingBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddAbilityBehaviorsArtifactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddBanAmountBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddEndOfRoundRerollBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddMonkeyMoneyBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddProjectileBehaviorsArtifactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddRewardChoiceBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddRogueBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddRogueXpBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddTowerBehaviorsArtifactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.AddWeaponBehaviorsArtifactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.ArtifactDamageModifierIncludeDotsModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.BoostRerollCostModifierModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.CatsMeowBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.CooldownBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.CountAllCategoriesBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.DamageBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.DiscountBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.FollowMeBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.GiveCritChanceBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.GlobalTechBotLinkBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.GluesplosionBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.HeroEndOfRoundXpScaleBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.HeroXpPerBloonLayerBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.InstaCooldownBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.InvokeBoostBuffBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.PierceBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.PopsFiredBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.ProjectileCarouselBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.ProjectileLifespanBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.ProjectileSpeedBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.RangeBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.RateBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.RemoveArtifactAfterEndOfGameRerollBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.RemoveArtifactIfNoTowerBanAmountBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.Behaviors.SpreadBoostBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.BoostArtifactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.ItemArtifactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Artifacts.MapArtifactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Audio.SoundModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.Actions.DashForwardsActionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.AddHeatActionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.BlastapopoulosRushBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.BloonariusRushBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.CreateEffectOnPopModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.DamageOverTimeCustomModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.DreadbloonRushBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.OnDamagedTriggerModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.PhayzeRushBehaviorModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.PlayAnimTriggerActionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.QuickEntryModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.SetInvulnerableActionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.SetPositionActionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.SetSpeedPercentActionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.SlowTowersOnPopModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Bloons.Behaviors.WaitForSecondsActionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.MapEditorAreaData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.MapEditorPathData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.MapEditorPowerData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.MapEditorPropData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.MapEditorStampData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.MapEditorTowerData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.PathNode" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.PositionalData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.AllBloonSpeedModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.BloonHealthModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.BloonHealthModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.BonusCashPerRoundModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.ChimpsModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.DeflationModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.DisableContinueModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.DisableMonkeyKnowledgeModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.DisableSellTowerModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.EndRoundModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.GlobalCostModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.GlobalSpeedModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.ImpoppableModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.LockTowerModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.LockTowerSetModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.MaxHealthModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.ModifyAllCashModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.ModifyPopCashModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.ModifyRoundCashModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.ModifyTowerCashModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.MonkeyMoneyModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.RemoveManaShieldModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.ReverseModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.RoundSetModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.SellMultiplierModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.SetHealthForBloonModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.StartingCashModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.StartingHealthModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Gameplay.Mods.StartingRoundModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Map.AreaData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Map.RemovableAreaData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.MapEditorBehaviors.EditorLayerBehaviors.MapEditorLayerBehaviorsMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Rounds.FreeplayBloonGroupModel/Bounds" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Rounds.RoundThresholdMultiplier" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.SimulationBehaviors.Mods.HeroXPBonusModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.SimulationBehaviors.SimulationBehaviorsMod" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.TowerSets.TowerSet" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.Abilities.Behaviors.BonusLivesOnAbilityModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.Attack.Behaviors.RotateToPointerModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.Attack.Behaviors.TargetPointerModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.BonusLivesPerRoundModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.CreateProjectileOnTowerDestroyModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.DamageModifierSupportModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.DamageTypeSupportModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.Emissions.ArcEmissionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.Emissions.EmissionAtClosestPathSegmentModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.Emissions.SingleEmissionAtTowerModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.Emissions.SingleEmissionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.OverrideCamoDetectionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.RateSupportModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.SlowBloonsZoneModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.StartOfRoundRateBuffModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.TowerCreateProjectileOnProjectileExhaustModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.VisibilitySupportModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.WeaponReloadPercentageOnTargetPrioModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Filters.FilterInvisibleModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Filters.FilterMoabModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.AddBehaviorsToBloonModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.AddOverlayToBloonModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.AddSlowImmunityToBloonModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.BouncingDartsModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.HeroXpInjectionModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.AddBehaviorToBloonModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.AddBonusDamagePerHitToBloonModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.AdoraTrackTargetModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.AgeModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.AgeRandomModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.ClearHitBloonsModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.ClearHitBloonsWhenNoLongerCollidingModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.CreateEffectOnContactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.CreateEffectOnExhaustFractionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.CreateSoundOnProjectileCollisionModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.DamageModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.DamageModifierForBloonStateModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.DamageModifierForModifiersModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.DamageModifierForRoundModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.DamageModifierForTagModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.DamageModifierWrathModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.FreezeModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.IncreaseBloonWorthModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.IncreaseBloonWorthWithTierModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.InstantModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.KnockbackModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.MapBorderReboundModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.ProjectileBlockerCollisionReboundModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.ProjectileFilterModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.ProjectileZeroRotationModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.PushBackModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.RetargetOnContactModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.SlowMaimMoabModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.SlowModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.SlowModifierForTagModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.SlowOnPopModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.StripChildrenModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.TrackTargetModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.TrackTargetOrOrbitTowerModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.TravelAlongPathModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.TravelCurvyModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.WindModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.Behaviors.ZigZagModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Projectiles.ProjectileModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Weapons.Behaviors.BloonsInRangeAttackSpeedModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Weapons.Behaviors.RandomAngleOffsetModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Weapons.Behaviors.RandomRateModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Weapons.Behaviors.RandomSpeedOffsetModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.BroadcastSignalAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.CreateHandDragObjectAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.CreatePlacementCircleAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.GainCashAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.LoseCashAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.ManageMainHudAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.ManageShopPanelAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.ManageTowerUpgradesPanelAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.PlayMovieClipAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.RemoveAllHandDragObjectsAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.RemoveAllPlacementCirclesAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.SelectPlacedTowerAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.SendBloonAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.ShowDialogueAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.ShowGlowOnTowerButtonAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.StartRoundAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.ToggleMapEditorLayer" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.ToggleTowerUpgradeScreenAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Actions.TriggerEventHooksAction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.HookCondition" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnBloonDestroyedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnBloonLeakedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnCloseSceneTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnMatchReadyTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnPlacementCircleInteraction" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnRecieveSignalTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnRoundEndedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnRoundStartedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnTimePassedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnTowerPlacedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnTowerUpgradeUnlockedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Behaviors.Events.Triggers.OnTowerUpgradedTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.MapEditor.EditorLayerBehaviors.DisablePropsOnLayerOnLoad" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.AbilityRefreshOnTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.AddArtifactToSimulation" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.AddEventHook" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.AddMapAssetsEvent" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.AnniversaryChallengeQuest" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.AwardCashOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.BossRushManager" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.CashRushGameMode" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.DisallowTowersUntilOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.EnduranceRaceMode" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.FastUpgradesGameMode" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.HighlightAbilitiesOnTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.OnBloonInRangeOfHeroDialogueTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.PropSpawningChallenge" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.RefreshHeroCooldownsOnRoundEnd" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.RemovePropsOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.RemoveRemoveablesOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.RogueQuestMode" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.SetHeroLevelOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.SpawnPowerAtLocation" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.SpawnPropOnRoundOrTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.SpawnTowerOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.SwitchMusicTrackTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.ToggleAbilityCooldownOnTrigger" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.TrackBloonDistanceTraveledOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.TriggerBehaviorsOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.TriggerDialogueOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.TriggerGameModeBehaviors" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.TriggerVictoryOnBossDefeat" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.SimulationBehaviors.UpgradeTowerOnRound" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Towers.Pets.PetCreateEffectOnPlace" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Towers.Pets.PetCreateEffectOnSell" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Towers.Pets.PetCreateSoundOnIdle" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Towers.Pets.PetCreateSoundOnPlace" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Towers.Pets.Wander" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Legends.LegendsType" preserve="nothing" serialized="true" />
    <type fullname="BloonOverlayClass" preserve="nothing" serialized="true" />
    <type fullname="BloonProperties" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/BloonHealthMultipliersData" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/BloonModifiersData" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/EditorPowerData" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/EditorTowerData" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorAreaAssets" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorAreaDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorPathAssets" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorPathDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorPowerAssets" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorPowerDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorPropAssets" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorPropDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorStampAssets" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorStampDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorTowerAssets" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/MapEditorTowerDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/PowerDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/PowerList" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/StartRulesData" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/TowerDataContainer" preserve="nothing" serialized="true" />
    <type fullname="DailyChallengeData/TowerList" preserve="nothing" serialized="true" />
    <type fullname="RogueGameModeRules/RogueModeGenericRules" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Display.Animation.FrameOverrideData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.GeraldoItems.GeraldoItemModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Upgrades.UpgradeModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Simulation.Display.Mesh" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Cascade.CascadingFloat" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.EditorMenu/AreaEditingPanel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.EditorMenus.EditorMenu/PathEditingPanel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.MenuThemeManager/ThemeDictionary" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Utils.TouchGesture/GestureSettings" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Data.ScorePanelUI" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.AbilityCooldownPercentageModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.TowerSellModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.TowerXpModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Mods.WeaponReloadPercentageModModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.Scenes.DataConflictScreen/DataDisplay" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Quests.QuestBrowserScreen/TabButton" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Powers.PowerModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Behaviors.FootprintModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.TowerModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.Towers.Upgrades.UpgradePathModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Main.PowersSelect.PowersSelectScreen/PowerThemeData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.InGame.InGame/InGameMenuDef" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.TrophyStore.TrophyStoreScreen/TrophyStoreFilterData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Feats.FeatTheme" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.CustomMapModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ContentBrowser.MapEditorSettingsData" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ServerEvents.BloonHealthMultipliers" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ServerEvents.BloonModifiers" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ServerEvents.CashModifiers" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ServerEvents.DailyChallengeModel" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Models.ServerEvents.StartRules" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Achievements.AchievementTheme" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Odyssey.OdysseyWorldConfig" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.WorldInteractables.WorldConfig" preserve="nothing" serialized="true" />
    <type fullname="Assets.Cursor/CursorSprites" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Data.Languages.LanguageAsset/LanguageDict" preserve="nothing" serialized="true" />
    <type fullname="TowerSetBgSprites" preserve="nothing" serialized="true" />
    <type fullname="Assets.Scripts.Unity.UI_New.Upgrade.UpgradeDetails/UpgradeDetailsTheme" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="NinjaKiwi.Common, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="NinjaKiwi.Common.ResourceUtils.SpriteReleaser" preserve="all" />
    <type fullname="NK_TextMeshPro" preserve="all" />
    <type fullname="NK_TextMeshProUGUI" preserve="all" />
    <type fullname="TextRoller" preserve="all" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.PrefabReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.AnimationClipReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.AssetLibrary`1/Entry" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.AssetLibrary`1/Group" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.AssetType" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.AudioClipReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.MaterialReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.ScriptableObjectReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.SpriteReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.SpriteShapeReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.TMP_FontAssetReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.TextAssetReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.TextureReference" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.SerializableDictionary`2" preserve="nothing" serialized="true" />
    <type fullname="NinjaKiwi.Common.ResourceUtils.VideoClipReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.2D.SpriteShape.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.U2D.SpriteShape" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteShapeController" preserve="all" />
    <type fullname="UnityEngine.U2D.AngleRange" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.U2D.CornerSprite" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.U2D.Spline" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.U2D.SplineControlPoint" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.InputSystem, Version=********, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.InputSystem.UI.MultiplayerEventSystem" preserve="all" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalCameraData" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.TemporalAA/Settings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AtlasSpriteProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshPro" preserve="all" />
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_Dropdown" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
    <type fullname="TMPro.GlyphAnchorPoint" preserve="nothing" serialized="true" />
    <type fullname="TMPro.LigatureSubstitutionRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.MarkPositionAdjustment" preserve="nothing" serialized="true" />
    <type fullname="TMPro.MarkToBaseAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.MarkToMarkAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionData" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionDataList" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SubmitEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TextSelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TouchScreenKeyboardEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEditor.Audio.AudioMixerSnapshotController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Animation" preserve="all" />
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.AnimatorOverrideController" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Audio.AudioMixer" preserve="all" />
    <type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="all" />
    <type fullname="UnityEngine.AudioClip" preserve="all" />
    <type fullname="UnityEngine.AudioListener" preserve="all" />
    <type fullname="UnityEngine.AudioSource" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.Cubemap" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LightmapSettings" preserve="all" />
    <type fullname="UnityEngine.LineRenderer" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.MonoBehaviour" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.Rendering.SortingGroup" preserve="all" />
    <type fullname="UnityEngine.RenderSettings" preserve="all" />
    <type fullname="UnityEngine.RenderTexture" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.TrailRenderer" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.GridModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Grid" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.SpriteShapeModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.U2D.SpriteShapeRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.EventSystems.EventTrigger" preserve="all" />
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.Outline" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.Scrollbar" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Selectable" preserve="all" />
    <type fullname="UnityEngine.UI.Slider" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.ToggleGroup" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.VideoModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Video.VideoClip" preserve="all" />
    <type fullname="UnityEngine.Video.VideoPlayer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.WindModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.WindZone" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphValueRecord" preserve="nothing" serialized="true" />
  </assembly>
</linker>