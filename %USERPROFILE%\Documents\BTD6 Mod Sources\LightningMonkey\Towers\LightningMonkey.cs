using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.TowerSets;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;
using Il2CppAssets.Scripts.Models.Towers.Weapons;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers
{
    public class LightningMonkeyTower : ModTower
    {
        // Use Druid as base for natural lightning abilities and staff appearance
        public override string BaseTower => TowerType.Druid;
        public override TowerSet TowerSet => TowerSet.Magic;

        public override string DisplayName => "Lightning Monkey";
        public override string Description => "A mystical monkey wielding the power of lightning! Shoots crackling bolts that chain through bloons with devastating electrical energy.";
        public override int Cost => 650;

        // Full upgrade paths
        public override int TopPathUpgrades => 5;
        public override int MiddlePathUpgrades => 5;
        public override int BottomPathUpgrades => 5;

        public override void ModifyBaseTowerModel(TowerModel towerModel)
        {
            // Enhanced range for lightning staff
            towerModel.range += 15f;
            
            var attackModel = towerModel.GetAttackModel();
            if (attackModel != null)
            {
                var weaponModel = attackModel.weapons[0];
                
                // Enhanced lightning projectile stats
                weaponModel.Rate = 1.0f; // Attack every 1 second
                var projectile = weaponModel.projectile;
                
                // Base lightning stats
                projectile.pierce = 4f;
                projectile.GetDamageModel().damage = 2f;
                projectile.scale = 1.2f; // Larger lightning bolts
                
                // Try to use Druid's lightning projectile for better visuals
                try
                {
                    var druid200 = Game.instance.model.GetTowerFromId("Druid-200");
                    if (druid200 != null)
                    {
                        var druidLightning = druid200.GetAttackModel().weapons[0].projectile.Duplicate();
                        druidLightning.pierce = projectile.pierce;
                        druidLightning.GetDamageModel().damage = projectile.GetDamageModel().damage;
                        druidLightning.scale = projectile.scale;
                        weaponModel.projectile = druidLightning;
                    }
                }
                catch
                {
                    // Fallback to original projectile if Druid lightning not available
                }
            }
        }
    }
}
