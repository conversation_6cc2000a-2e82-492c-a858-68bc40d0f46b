@echo off
echo Creating Lightning Monkey based on Druid for better visuals...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

echo Writing Lightning Monkey with Druid base...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.Druid;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "A mystical monkey wielding the power of lightning! Shoots crackling bolts that chain through bloons.";
echo     public override int Cost =^> 550;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 12f;
echo       var atk = m.GetAttackModel^(^);
echo       atk.weapons[0].Rate = 0.9f;
echo       var p = atk.weapons[0].projectile;
echo       p.pierce += 2;
echo       p.GetDamageModel^(^).damage = 2;
echo       p.scale = 1.3f;
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building Lightning Monkey with Druid base...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey with Druid visuals!
    echo ========================================
    echo.
    echo Visual improvements:
    echo - Based on Druid ^(monkey with staff like your image^)
    echo - Natural lightning chain effects from Druid abilities
    echo - Enhanced stats: +12 range, 2 damage, +2 pierce
    echo - Larger projectiles ^(1.3x scale^)
    echo - Faster attack rate ^(0.9s^)
    echo.
    echo The Druid base gives you:
    echo - A monkey holding a wooden staff
    echo - Natural magic/lightning abilities
    echo - Chain lightning projectiles
    echo - Mystical appearance perfect for lightning powers
    echo.
    echo Launch BTD6 to see your Lightning Monkey!
    echo ^(Look in Magic category, cost 550^)
) else (
    echo Build failed. Check errors above.
)
pause
