@echo off
echo Creating COMPLETE upgrade structure with ALL tiers 1-5...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"

echo Removing old upgrades and creating FULL structure...
if exist "%UPGRADES_DIR%" rmdir /s /q "%UPGRADES_DIR%"
mkdir "%UPGRADES_DIR%"

echo Creating ALL TOP PATH upgrades (T1-T5)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class DarkBoost : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 1;
echo     public override int Cost =^> 200;
echo     public override string DisplayName =^> "Dark Boost";
echo     public override string Description =^> "Lightning infused with dark energy. +1 damage, +2 pierce.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.pierce += 2f;
echo         projectile.GetDamageModel^(^).damage += 1f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\DarkBoost.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class ShadowStrike : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 2;
echo     public override int Cost =^> 400;
echo     public override string DisplayName =^> "Shadow Strike";
echo     public override string Description =^> "Dark lightning strikes faster. +1 damage, 15%% faster.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.GetDamageModel^(^).damage += 1f;
echo         attackModel.weapons[0].Rate *= 0.85f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\ShadowStrike.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class VoidChanneling : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 3;
echo     public override int Cost =^> 1000;
echo     public override string DisplayName =^> "Void Channeling";
echo     public override string Description =^> "Channels void energy. +2 damage, +3 pierce.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.GetDamageModel^(^).damage += 2f;
echo         projectile.pierce += 3f;
echo         projectile.scale *= 1.3f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\VoidChanneling.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class DarkMastery : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 4;
echo     public override int Cost =^> 8000;
echo     public override string DisplayName =^> "Dark Mastery";
echo     public override string Description =^> "Masters dark magic. +3 damage, +5 pierce, 25%% faster.";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.GetDamageModel^(^).damage += 3f;
echo         projectile.pierce += 5f;
echo         attackModel.weapons[0].Rate *= 0.75f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\DarkMastery.cs"

(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class VoidStormMaster : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 5;
echo     public override int Cost =^> 45000;
echo     public override string DisplayName =^> "Void Storm Master";
echo     public override string Description =^> "DARK MAGIC: Creates devastating black holes!";
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.pierce = 15f;
echo         projectile.GetDamageModel^(^).damage = 25f;
echo         projectile.scale = 2.0f;
echo         var blackHole = projectile.Duplicate^(^);
echo         blackHole.pierce = 999f;
echo         blackHole.GetDamageModel^(^).damage = 50f;
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^("BlackHole", blackHole, new SingleEmissionModel^("", null^), true, false, false^)^);
echo         attackModel.weapons[0].Rate = 0.3f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\VoidStormMaster.cs"

echo Building with complete TOP path only...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! TOP PATH COMPLETE!
    echo ========================================
    echo.
    echo TOP PATH ^(Dark Magic^) - ALL TIERS:
    echo   T1: Dark Boost - +1 damage, +2 pierce ^(200^)
    echo   T2: Shadow Strike - +1 damage, 15%% faster ^(400^)
    echo   T3: Void Channeling - +2 damage, +3 pierce ^(1000^)
    echo   T4: Dark Mastery - +3 damage, +5 pierce, 25%% faster ^(8000^)
    echo   T5: Void Storm Master - BLACK HOLE MAGIC! ^(45000^)
    echo.
    echo Launch BTD6 to test! ^(Magic category, 750 cost^)
    echo The TOP path is now complete with black hole magic!
) else (
    echo Build failed. Check errors above.
)
pause
