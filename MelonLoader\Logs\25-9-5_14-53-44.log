
[14:53:45.060] ------------------------------
[14:53:45.066] MelonLoader v0.7.1 Open-Beta
[14:53:45.069] OS: Windows 11
[14:53:45.069] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[14:53:45.069] ------------------------------
[14:53:45.070] Game Type: Il2cpp
[14:53:45.071] Game Arch: x64
[14:53:45.071] ------------------------------
[14:53:45.071] Command-Line: 
[14:53:45.072] ------------------------------
[14:53:45.072] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[14:53:45.072] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[14:53:45.073] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[14:53:45.073] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[14:53:45.073] Runtime Type: net6
[14:53:45.144] ------------------------------
[14:53:45.144] Game Name: BloonsTD6
[14:53:45.145] Game Developer: Ninja Kiwi
[14:53:45.147] Unity Version: 6000.0.52f1
[14:53:45.147] Game Version: 50.1
[14:53:45.147] ------------------------------

[14:53:45.577] Preferences Loaded!

[14:53:45.587] Loading UserLibs...
[14:53:45.589] 0 UserLibs loaded.

[14:53:45.589] Loading Plugins...
[14:53:45.593] 0 Plugins loaded.

[14:53:45.985] Loading Il2CppAssemblyGenerator...
[14:53:46.022] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[14:53:46.611] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[14:53:46.611] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[14:53:46.613] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[14:53:46.613] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[14:53:46.619] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[14:53:46.619] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[14:53:46.619] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[14:53:46.619] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[14:53:46.619] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[14:53:46.620] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[14:53:46.620] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[14:53:46.620] [Il2CppAssemblyGenerator] Checking GameAssembly...
[14:53:46.756] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[14:53:46.757] Loading Mods...
[14:53:46.788] ------------------------------
[14:53:46.823] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[14:53:46.823] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[14:53:46.825] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[14:53:46.825] SHA256 Hash: '8FB218B4AFC5C5C0C45B55E3F0D2F4FD98A38E1B36B8289FED5BC3D23827F676'

[14:53:47.289] ------------------------------
[14:53:47.289] BloonsTD6 Mod Helper v3.4.12
[14:53:47.289] by Gurrenm4 and Doombubbles
[14:53:47.289] Assembly: Btd6ModHelper.dll
[14:53:47.290] ------------------------------
[14:53:47.292] ------------------------------
[14:53:47.292] Lightning Monkey Mod v1.0.0
[14:53:47.292] by You
[14:53:47.292] Assembly: LightningMonkey.dll
[14:53:47.292] ------------------------------
[14:53:47.292] ------------------------------
[14:53:47.293] 2 Mods loaded.

[14:53:48.291] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[14:53:48.462] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[14:53:48.485] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[14:53:48.488] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[14:53:48.500] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[14:53:48.502] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[14:53:48.911] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[14:53:49.402] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[14:53:49.649] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[14:53:58.466] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[14:53:58.468] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[15:52:55.067] Preferences Saved!
