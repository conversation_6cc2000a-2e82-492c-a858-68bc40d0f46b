@echo off
setlocal EnableExtensions DisableDelayedExpansion
cd /d "%~dp0"

echo ===== Verifying Mod Helper / MelonLoader setup =====

if not exist "Mods" (
  echo Mods folder not found here.
  echo Current dir: %cd%
) else (
  echo - Listing Mod Helper DLLs in Mods
  for /f "delims=" %%F in ('dir /b /s "Mods\BloonsTD6*Helper*.dll" 2^>nul') do echo %%F
)

echo.
if exist "MelonLoader\Latest.log" (
  echo - Extracting versions from MelonLoader\Latest.log
  findstr /C:"MelonLoader v" /C:"BTD6 Mod Helper" "MelonLoader\Latest.log"
) else (
  echo MelonLoader\Latest.log not found. Launch the game once to generate it.
)

echo.
echo If the Mod Menu is missing:
echo   1) Re-run MelonLoader installer on BloonsTD6.exe
_echo   2) Keep only ONE latest 'BloonsTD6 Mod Helper*.dll' in Mods

echo Done.
endlocal
pause
