
[18:55:48.803] ------------------------------
[18:55:48.805] MelonLoader v0.7.1 Open-Beta
[18:55:48.805] OS: Windows 11
[18:55:48.806] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[18:55:48.806] ------------------------------
[18:55:48.807] Game Type: Il2cpp
[18:55:48.807] Game Arch: x64
[18:55:48.807] ------------------------------
[18:55:48.807] Command-Line: 
[18:55:48.807] ------------------------------
[18:55:48.808] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[18:55:48.808] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[18:55:48.808] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[18:55:48.808] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[18:55:48.808] Runtime Type: net6
[18:55:48.870] ------------------------------
[18:55:48.870] Game Name: BloonsTD6
[18:55:48.871] Game Developer: Ninja Kiwi
[18:55:48.872] Unity Version: 6000.0.52f1
[18:55:48.872] Game Version: 50.1
[18:55:48.873] ------------------------------

[18:55:49.254] Preferences Loaded!

[18:55:49.265] Loading UserLibs...
[18:55:49.267] 0 UserLibs loaded.

[18:55:49.267] Loading Plugins...
[18:55:49.271] 0 Plugins loaded.

[18:55:49.657] Loading Il2CppAssemblyGenerator...
[18:55:49.694] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[18:55:49.876] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[18:55:49.876] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[18:55:49.877] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[18:55:49.877] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[18:55:49.883] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[18:55:49.883] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[18:55:49.883] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[18:55:49.883] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[18:55:49.883] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[18:55:49.884] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[18:55:49.884] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[18:55:49.884] [Il2CppAssemblyGenerator] Checking GameAssembly...
[18:55:50.019] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[18:55:50.020] Loading Mods...
[18:55:50.052] ------------------------------
[18:55:50.090] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[18:55:50.090] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[18:55:50.092] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[18:55:50.092] SHA256 Hash: 'C23277E37C12E91D3C47EB6A2548626CCE3111679FC4770DDEA2C06B5B77C9E9'

[18:55:50.592] ------------------------------
[18:55:50.593] BloonsTD6 Mod Helper v3.4.12
[18:55:50.593] by Gurrenm4 and Doombubbles
[18:55:50.593] Assembly: Btd6ModHelper.dll
[18:55:50.594] ------------------------------
[18:55:50.596] ------------------------------
[18:55:50.596] Lightning Monkey Mod v1.0.0
[18:55:50.596] by You
[18:55:50.596] Assembly: LightningMonkey.dll
[18:55:50.596] ------------------------------
[18:55:50.596] ------------------------------
[18:55:50.596] 2 Mods loaded.

[18:55:51.553] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[18:55:51.723] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[18:55:51.745] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[18:55:51.749] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[18:55:51.760] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[18:55:51.762] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[18:55:52.143] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[18:55:52.613] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[18:55:52.947] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[18:55:58.529] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 382 mods over 5.9 seconds
[18:56:01.839] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[18:56:01.842] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[18:57:34.736] [Il2CppInterop] During invoking native->managed trampoline
Il2CppInterop.Runtime.Il2CppException: System.NullReferenceException: Object reference not set to an instance of an object.
--- BEGIN IL2CPP STACK TRACE ---
System.NullReferenceException: Object reference not set to an instance of an object.
  at Assets.Scripts.Simulation.Towers.Behaviors.HeliMovement.OnChangeTargetPriority (Assets.Scripts.Models.Towers.TargetType targetType) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.SetTargetType (Assets.Scripts.Models.Towers.TargetType type) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.Initialise (Assets.Scripts.Simulation.Objects.Entity target, Assets.Scripts.Models.Model modelToUse) [0x00000] in <00000000000000000000000000000000>:0 
--- END IL2CPP STACK TRACE ---

   at Il2CppInterop.Runtime.Il2CppException.RaiseExceptionIfNecessary(IntPtr returnedException) in /home/<USER>/work/Il2CppInterop/Il2CppInterop/Il2CppInterop.Runtime/Il2CppException.cs:line 36
   at DMD<Il2CppAssets.Scripts.Simulation.Towers.Tower::Initialise>(Tower this, Entity target, Model modelToUse)
   at (il2cpp -> managed) Initialise(IntPtr , IntPtr , IntPtr , Il2CppMethodInfo* )
[18:58:11.588] Preferences Saved!
