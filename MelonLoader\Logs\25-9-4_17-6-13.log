
[17:06:13.402] ------------------------------
[17:06:13.405] MelonLoader v0.7.1 Open-Beta
[17:06:13.406] OS: Windows 11
[17:06:13.406] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[17:06:13.406] ------------------------------
[17:06:13.407] Game Type: Il2cpp
[17:06:13.407] Game Arch: x64
[17:06:13.407] ------------------------------
[17:06:13.407] Command-Line: 
[17:06:13.407] ------------------------------
[17:06:13.407] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:06:13.407] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:06:13.407] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[17:06:13.407] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[17:06:13.407] Runtime Type: net6
[17:06:13.473] ------------------------------
[17:06:13.473] Game Name: BloonsTD6
[17:06:13.473] Game Developer: Ninja Kiwi
[17:06:13.475] Unity Version: 6000.0.52f1
[17:06:13.475] Game Version: 50.1
[17:06:13.475] ------------------------------

[17:06:13.870] Preferences Loaded!

[17:06:13.883] Loading UserLibs...
[17:06:13.885] 0 UserLibs loaded.

[17:06:13.885] Loading Plugins...
[17:06:13.889] 0 Plugins loaded.

[17:06:14.523] Loading Il2CppAssemblyGenerator...
[17:06:14.612] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[17:06:14.823] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[17:06:14.823] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[17:06:14.824] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[17:06:14.824] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[17:06:14.833] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[17:06:14.833] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[17:06:14.833] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[17:06:14.833] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[17:06:14.833] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[17:06:14.834] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[17:06:14.834] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[17:06:14.834] [Il2CppAssemblyGenerator] Checking GameAssembly...
[17:06:15.006] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[17:06:15.006] Loading Mods...
[17:06:15.045] ------------------------------
[17:06:15.086] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[17:06:15.086] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[17:06:15.087] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[17:06:15.087] SHA256 Hash: 'C8FBC37D465D036DB0B4FB34F566E8DB6E5DDE97646C08671DCE66C19F00B8DB'

[17:06:15.664] ------------------------------
[17:06:15.665] BloonsTD6 Mod Helper v3.4.12
[17:06:15.665] by Gurrenm4 and Doombubbles
[17:06:15.665] Assembly: Btd6ModHelper.dll
[17:06:15.665] ------------------------------
[17:06:15.667] ------------------------------
[17:06:15.667] Lightning Monkey Mod v1.0.0
[17:06:15.667] by You
[17:06:15.667] Assembly: LightningMonkey.dll
[17:06:15.667] ------------------------------
[17:06:15.667] ------------------------------
[17:06:15.667] 2 Mods loaded.

[17:06:16.703] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[17:06:16.872] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[17:06:16.897] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[17:06:16.901] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[17:06:16.914] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[17:06:16.916] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[17:06:17.333] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[17:06:17.786] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[17:06:18.146] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[17:06:27.744] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 382 mods over 9.9 seconds
[17:06:27.994] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[17:06:27.996] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[05:00:24.006] Preferences Saved!
