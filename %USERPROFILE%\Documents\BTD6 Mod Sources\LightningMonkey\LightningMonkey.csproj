<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <AssemblyName>LightningMonkey</AssemblyName>
    <RootNamespace>LightningMonkey</RootNamespace>
  </PropertyGroup>

  <Import Project="..\btd6.targets" />

  <ItemGroup>
    <EmbeddedResource Include="Resources\**\*.png" />
  </ItemGroup>

  <Target Name="AfterBuild" AfterTargets="Build">
    <Copy SourceFiles="$(TargetDir)$(TargetFileName)" 
          DestinationFolder="C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\Mods" 
          OverWriteReadOnlyFiles="true" />
  </Target>

</Project>
