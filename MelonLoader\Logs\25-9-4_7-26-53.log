
[07:26:54.377] ------------------------------
[07:26:54.378] MelonLoader v0.7.1 Open-Beta
[07:26:54.380] OS: Windows 11
[07:26:54.380] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[07:26:54.380] ------------------------------
[07:26:54.381] Game Type: Il2cpp
[07:26:54.381] Game Arch: x64
[07:26:54.381] ------------------------------
[07:26:54.381] Command-Line: 
[07:26:54.381] ------------------------------
[07:26:54.382] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[07:26:54.382] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[07:26:54.382] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[07:26:54.382] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[07:26:54.382] Runtime Type: net6
[07:26:54.444] ------------------------------
[07:26:54.444] Game Name: BloonsTD6
[07:26:54.445] Game Developer: Ninja Kiwi
[07:26:54.446] Unity Version: 6000.0.52f1
[07:26:54.446] Game Version: 50.1
[07:26:54.447] ------------------------------

[07:26:54.841] Preferences Loaded!

[07:26:54.853] Loading UserLibs...
[07:26:54.855] 0 UserLibs loaded.

[07:26:54.855] Loading Plugins...
[07:26:54.859] 0 Plugins loaded.

[07:26:55.496] Loading Il2CppAssemblyGenerator...
[07:26:55.537] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[07:26:55.778] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[07:26:55.778] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[07:26:55.778] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[07:26:55.779] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[07:26:55.784] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[07:26:55.784] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[07:26:55.785] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[07:26:55.785] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[07:26:55.785] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[07:26:55.785] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[07:26:55.785] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[07:26:55.785] [Il2CppAssemblyGenerator] Checking GameAssembly...
[07:26:55.920] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[07:26:55.921] Loading Mods...
[07:26:55.952] ------------------------------
[07:26:55.990] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[07:26:55.990] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[07:26:55.992] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[07:26:55.992] SHA256 Hash: '213E37E24AFDD769E79CC06C43EF2A6E098EBC11D58F870A8567538CD88470A4'

[07:26:56.504] ------------------------------
[07:26:56.504] BloonsTD6 Mod Helper v3.4.12
[07:26:56.504] by Gurrenm4 and Doombubbles
[07:26:56.505] Assembly: Btd6ModHelper.dll
[07:26:56.505] ------------------------------
[07:26:56.507] ------------------------------
[07:26:56.507] Lightning Monkey Mod v1.0.0
[07:26:56.507] by You
[07:26:56.507] Assembly: LightningMonkey.dll
[07:26:56.507] ------------------------------
[07:26:56.508] ------------------------------
[07:26:56.508] 2 Mods loaded.

[07:26:57.478] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[07:26:57.650] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[07:26:57.672] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[07:26:57.675] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[07:26:57.687] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[07:26:57.689] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[07:26:58.092] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[07:26:58.544] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[07:26:58.893] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[07:27:05.571] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 382 mods over 7.0 seconds
[07:27:07.786] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[07:27:07.788] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[07:32:51.338] Preferences Saved!
