@echo off
setlocal EnableExtensions EnableDelayedExpansion
cd /d "%~dp0"

echo ================= Clean BTD6 Mod Helper (safe) =================
echo This will:
echo   - Detect duplicate 'BloonsTD6 Mod Helper*.dll' files in Mods
n echo   - Keep the newest copy and move older copies into Mods\_backup_ModHelper
n echo   - Optionally clear MelonLoader caches (they regenerate)

echo.
if not exist "Mods" (
  echo ERROR: Mods folder not found here: "%cd%"
  echo Run this script from your BTD6 install folder.
  goto :END
)

set NEWEST=
set COUNT=0
for /f "delims=" %%F in ('dir /b /o-d "Mods\BloonsTD6*Helper*.dll" 2^>nul') do (
  if not defined NEWEST set NEWEST=%%F
  set /a COUNT+=1
)

if not defined NEWEST (
  echo No 'BloonsTD6 Mod Helper*.dll' files found in Mods.
  echo Download the latest Mod Helper and place it into Mods.
  echo Link: https://github.com/gurrenm3/BTD-Mod-Helper/releases
  goto :CACHE
)

echo Detected %COUNT% Mod Helper file^(s^).
for /f "delims=" %%F in ('dir /b /o-d "Mods\BloonsTD6*Helper*.dll"') do (
  if /i "%%F"=="%NEWEST%" (
    echo KEEP: Mods\%%F (newest)
  ) else (
    set OLD=%%F
    if not exist "Mods\_backup_ModHelper" mkdir "Mods\_backup_ModHelper"
    echo MOVE: Mods\%%F  --^>  Mods\_backup_ModHelper\%%F
    move /y "Mods\%%F" "Mods\_backup_ModHelper\%%F" >nul
  )
)

echo.
:CACHE
echo Optional: Clear caches (safe). This fixes some patch issues.
choice /c YN /n /m "Clear MelonLoader caches now? [Y/N]: "
if errorlevel 2 goto :SKIPCACHE

for %%D in ("MelonLoader\Dependencies" "MelonLoader\Managed" "UserData\BTD6ModHelper") do (
  if exist %%D (
    echo Deleting %%D ...
    rmdir /s /q %%D
  )
)
:SKIPCACHE

echo.
echo Next steps:
echo   1) Ensure there is EXACTLY ONE 'BloonsTD6 Mod Helper*.dll' in Mods.
echo   2) If none present, download latest from:
 echo      https://github.com/gurrenm3/BTD-Mod-Helper/releases
 echo      and place it in Mods folder.
 echo   3) Launch BTD6. The Mod Menu should be back.

echo Done.
:END
endlocal
pause
