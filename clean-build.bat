@echo off
echo Creating clean Lightning Monkey mod...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"

echo Writing clean tower file...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.DartMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "Channels crackling bolts that chain through bloons.";
echo     public override int Cost =^> 550;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 8f;
echo       var atk = m.GetAttackModel^(^);
echo       atk.weapons[0].Rate *= 0.95f;
echo       var p = atk.weapons[0].projectile;
echo       p.pierce += 1;
echo       p.GetDamageModel^(^).damage += 1;
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Writing clean forked lightning upgrade...
(
echo using BTD_Mod_Helper.Api.Towers; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class ForkedLightning : ModUpgrade^<LightningMonkey.Towers.LightningMonkeyTower^> { public override string DisplayName=^>"Forked Lightning"; public override string Description=^>"Fires faster with more pierce."; public override int Path=^>MIDDLE; public override int Tier=^>3; public override int Cost=^>2400; public override void ApplyUpgrade^(TowerModel m^){ var w=m.GetAttackModel^(^).weapons[0]; w.Rate *= 0.9f; w.projectile.pierce += 2; } } }
) > "%UPGRADES_DIR%\Mid_T3_ForkedLightning.cs"

echo Building final version...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey mod is ready!
    echo ========================================
    echo.
    echo The mod has been built and copied to your Mods folder.
    echo Launch BTD6 to see your new Lightning Monkey in the Magic category!
    echo.
    echo Features:
    echo - Lightning Monkey tower ^(Magic, 550 cost^)
    echo - 15 upgrades across 3 paths ^(5/5/5^)
    echo - Uses lightning projectiles that chain through bloons
    echo.
    echo Enjoy your new Lightning Monkey!
) else (
    echo Build failed. Check errors above.
)
pause
