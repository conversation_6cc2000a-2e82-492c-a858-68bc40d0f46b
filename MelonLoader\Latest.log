
[20:33:10.178] ------------------------------
[20:33:10.180] MelonLoader v0.7.1 Open-Beta
[20:33:10.181] OS: Windows 11
[20:33:10.182] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[20:33:10.182] ------------------------------
[20:33:10.182] Game Type: Il2cpp
[20:33:10.183] Game Arch: x64
[20:33:10.183] ------------------------------
[20:33:10.183] Command-Line: 
[20:33:10.183] ------------------------------
[20:33:10.183] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[20:33:10.184] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[20:33:10.184] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[20:33:10.184] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[20:33:10.184] Runtime Type: net6
[20:33:10.252] ------------------------------
[20:33:10.253] Game Name: BloonsTD6
[20:33:10.253] Game Developer: Ninja Kiwi
[20:33:10.255] Unity Version: 6000.0.52f1
[20:33:10.255] Game Version: 50.2
[20:33:10.255] ------------------------------

[20:33:10.648] Preferences Loaded!

[20:33:10.661] Loading UserLibs...
[20:33:10.663] 0 UserLibs loaded.

[20:33:10.663] Loading Plugins...
[20:33:10.667] 0 Plugins loaded.

[20:33:11.339] Loading Il2CppAssemblyGenerator...
[20:33:11.376] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[20:33:11.590] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[20:33:11.590] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[20:33:11.591] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[20:33:11.591] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[20:33:11.597] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[20:33:11.598] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[20:33:11.598] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[20:33:11.598] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[20:33:11.598] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[20:33:11.599] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[20:33:11.599] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[20:33:11.599] [Il2CppAssemblyGenerator] Checking GameAssembly...
[20:33:11.769] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[20:33:11.771] Loading Mods...
[20:33:11.812] ------------------------------
[20:33:11.848] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[20:33:11.849] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[20:33:11.851] Melon Assembly loaded: '.\Mods\EditPlayerData.dll'
[20:33:11.852] SHA256 Hash: 'D0AE1A904057FF46AF12EA6A6856E28954B9DE4191999802288DA1DD00C4483D'
[20:33:11.853] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[20:33:11.853] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[20:33:11.859] Melon Assembly loaded: '.\Mods\IndustrialFarmer.dll'
[20:33:11.860] SHA256 Hash: '55C6358F421E23428D41BA902A7B3DC9D22F626234AD9CC3BAE27C032087F2D3'
[20:33:11.861] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[20:33:11.861] SHA256 Hash: 'C23277E37C12E91D3C47EB6A2548626CCE3111679FC4770DDEA2C06B5B77C9E9'
[20:33:11.866] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[20:33:11.866] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[20:33:11.950] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[20:33:11.951] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[20:33:11.961] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[20:33:11.961] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[20:33:12.571] ------------------------------
[20:33:12.571] BloonsTD6 Mod Helper v3.4.12
[20:33:12.571] by Gurrenm4 and Doombubbles
[20:33:12.572] Assembly: Btd6ModHelper.dll
[20:33:12.572] ------------------------------
[20:33:12.575] ------------------------------
[20:33:12.575] Ultimate Crosspathing v1.7.1
[20:33:12.575] by doombubbles
[20:33:12.575] Assembly: UltimateCrosspathing.dll
[20:33:12.575] ------------------------------
[20:33:12.578] ------------------------------
[20:33:12.578] EditPlayerData v1.5.1
[20:33:12.578] by MaliciousFiles
[20:33:12.578] Assembly: EditPlayerData.dll
[20:33:12.579] ------------------------------
[20:33:12.581] ------------------------------
[20:33:12.581] Faster Forward v1.1.5
[20:33:12.581] by doombubbles
[20:33:12.581] Assembly: FasterForward.dll
[20:33:12.582] ------------------------------
[20:33:12.584] ------------------------------
[20:33:12.584] Industrial Farmer v1.0.15
[20:33:12.584] by doombubbles
[20:33:12.584] Assembly: IndustrialFarmer.dll
[20:33:12.584] ------------------------------
[20:33:12.587] ------------------------------
[20:33:12.587] Lightning Monkey Mod v1.0.0
[20:33:12.587] by You
[20:33:12.587] Assembly: LightningMonkey.dll
[20:33:12.587] ------------------------------
[20:33:12.590] ------------------------------
[20:33:12.590] Powers in Shop v3.0.3
[20:33:12.590] by doombubbles
[20:33:12.590] Assembly: PowersInShop.dll
[20:33:12.590] ------------------------------
[20:33:12.593] ------------------------------
[20:33:12.593] Unlimited 5th Tiers + v1.1.9
[20:33:12.593] by doombubbles
[20:33:12.593] Assembly: Unlimited5thTiers.dll
[20:33:12.594] ------------------------------
[20:33:12.594] ------------------------------
[20:33:12.594] 8 Mods loaded.

[20:33:13.685] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[20:33:13.861] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[20:33:13.883] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[20:33:13.887] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[20:33:13.899] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[20:33:13.901] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[20:33:13.991] [Il2CppInterop] Method Void SetNumColumns(Int32, Int32[]) on type EditPlayerData.UI.ModHelperTable has unsupported parameter Int32[] colFlex of type System.Int32[]
[20:33:13.993] [Il2CppInterop] Method Void SetSetting(EditPlayerData.UI.PlayerDataSetting) on type EditPlayerData.UI.PlayerDataSettingDisplay has unsupported parameter EditPlayerData.UI.PlayerDataSetting setting of type EditPlayerData.UI.PlayerDataSetting
[20:33:14.334] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[20:33:14.822] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[20:33:15.065] [EditPlayerData] Melon Assembly loaded: 'C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Windows.Forms.dll'
[20:33:15.066] [EditPlayerData] SHA256 Hash: 'F8EE5BFDC1FE7508773CEB997F6C78FC8CF8CDBAE11F0DEB15E78AA94BEBFED7'
[20:33:15.067] [EditPlayerData] EditPlayerData loaded!
[20:33:15.197] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[20:33:15.819] [BloonsTD6_Mod_Helper] AlchemistLoader finished loading bytes
[20:33:15.878] [BloonsTD6_Mod_Helper] BananaFarmLoader finished loading bytes
[20:33:15.913] [BloonsTD6_Mod_Helper] BananaFarmerProLoader finished loading bytes
[20:33:16.115] [BloonsTD6_Mod_Helper] BeastHandlerLoader finished loading bytes
[20:33:16.200] [BloonsTD6_Mod_Helper] BombShooterLoader finished loading bytes
[20:33:16.272] [BloonsTD6_Mod_Helper] BoomerangMonkeyLoader finished loading bytes
[20:33:16.332] [BloonsTD6_Mod_Helper] DartMonkeyLoader finished loading bytes
[20:33:16.493] [BloonsTD6_Mod_Helper] DartlingGunnerLoader finished loading bytes
[20:33:16.603] [BloonsTD6_Mod_Helper] DesperadoLoader finished loading bytes
[20:33:16.775] [BloonsTD6_Mod_Helper] DruidLoader finished loading bytes
[20:33:16.927] [BloonsTD6_Mod_Helper] EngineerMonkeyLoader finished loading bytes
[20:33:17.028] [BloonsTD6_Mod_Helper] GlueGunnerLoader finished loading bytes
[20:33:17.194] [BloonsTD6_Mod_Helper] HeliPilotLoader finished loading bytes
[20:33:17.408] [BloonsTD6_Mod_Helper] IceMonkeyLoader finished loading bytes
[20:33:17.617] [BloonsTD6_Mod_Helper] MermonkeyLoader finished loading bytes
[20:33:17.710] [BloonsTD6_Mod_Helper] MonkeyAceLoader finished loading bytes
[20:33:17.882] [BloonsTD6_Mod_Helper] MonkeyBuccaneerLoader finished loading bytes
[20:33:18.039] [BloonsTD6_Mod_Helper] MonkeySubLoader finished loading bytes
[20:33:18.186] [BloonsTD6_Mod_Helper] MonkeyVillageLoader finished loading bytes
[20:33:18.261] [BloonsTD6_Mod_Helper] MortarMonkeyLoader finished loading bytes
[20:33:18.348] [BloonsTD6_Mod_Helper] NinjaMonkeyLoader finished loading bytes
[20:33:18.402] [BloonsTD6_Mod_Helper] SniperMonkeyLoader finished loading bytes
[20:33:18.495] [BloonsTD6_Mod_Helper] SpikeFactoryLoader finished loading bytes
[20:33:18.533] [BloonsTD6_Mod_Helper] SuperMonkeyBeaconLoader finished loading bytes
[20:33:18.820] [BloonsTD6_Mod_Helper] SuperMonkeyLoader finished loading bytes
[20:33:18.881] [BloonsTD6_Mod_Helper] TackShooterLoader finished loading bytes
[20:33:19.047] [BloonsTD6_Mod_Helper] WizardMonkeyLoader finished loading bytes
[20:33:21.455] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 356 mods over 6.6 seconds
[20:33:24.727] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[20:33:24.730] [BloonsTD6_Mod_Helper] Registering ModContent for Ultimate Crosspathing...
[20:33:24.790] [Ultimate_Crosspathing] Finished loading DartMonkeys!
[20:33:24.887] [Ultimate_Crosspathing] Finished loading BoomerangMonkeys!
[20:33:24.935] [Ultimate_Crosspathing] Finished loading BombShooters!
[20:33:24.971] [Ultimate_Crosspathing] Finished loading TackShooters!
[20:33:25.010] [Ultimate_Crosspathing] Finished loading IceMonkeys!
[20:33:25.066] [Ultimate_Crosspathing] Finished loading GlueGunners!
[20:33:25.113] [Ultimate_Crosspathing] Finished loading Desperados!
[20:33:25.141] [Ultimate_Crosspathing] Finished loading SniperMonkeys!
[20:33:25.197] [Ultimate_Crosspathing] Finished loading MonkeySubs!
[20:33:25.275] [Ultimate_Crosspathing] Finished loading MonkeyBuccaneers!
[20:33:25.333] [Ultimate_Crosspathing] Finished loading MonkeyAces!
[20:33:25.393] [Ultimate_Crosspathing] Finished loading HeliPilots!
[20:33:25.434] [Ultimate_Crosspathing] Finished loading MortarMonkeys!
[20:33:25.468] [Ultimate_Crosspathing] Finished loading DartlingGunners!
[20:33:25.521] [Ultimate_Crosspathing] Finished loading WizardMonkeys!
[20:33:25.619] [Ultimate_Crosspathing] Finished loading SuperMonkeys!
[20:33:25.666] [Ultimate_Crosspathing] Finished loading NinjaMonkeys!
[20:33:25.716] [Ultimate_Crosspathing] Finished loading Alchemists!
[20:33:25.748] [Ultimate_Crosspathing] Finished loading Druids!
[20:33:25.837] [Ultimate_Crosspathing] Finished loading Mermonkeys!
[20:33:25.872] [Ultimate_Crosspathing] Finished loading BananaFarms!
[20:33:25.932] [Ultimate_Crosspathing] Finished loading SpikeFactorys!
[20:33:25.983] [Ultimate_Crosspathing] Finished loading MonkeyVillages!
[20:33:26.032] [Ultimate_Crosspathing] Finished loading EngineerMonkeys!
[20:33:26.079] [Ultimate_Crosspathing] Finished loading BeastHandlers!
[20:33:26.092] [Ultimate_Crosspathing] Finished loading BananaFarmerPros!
[20:33:26.118] [Ultimate_Crosspathing] Finished loading SuperMonkeyBeacons!
[20:33:26.118] [BloonsTD6_Mod_Helper] Registering ModContent for EditPlayerData...
[20:33:26.118] [BloonsTD6_Mod_Helper] Registering ModContent for Industrial Farmer...
[20:33:26.173] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[20:33:26.341] [BloonsTD6_Mod_Helper] Registering ModContent for Powers in Shop...
[20:33:26.390] [BloonsTD6_Mod_Helper] Registering ModContent for Unlimited 5th Tiers +...
