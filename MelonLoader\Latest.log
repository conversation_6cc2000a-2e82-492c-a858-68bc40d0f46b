
[21:07:20.818] ------------------------------
[21:07:20.820] MelonLoader v0.7.1 Open-Beta
[21:07:20.821] OS: Windows 11
[21:07:20.821] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[21:07:20.821] ------------------------------
[21:07:20.822] Game Type: Il2cpp
[21:07:20.822] Game Arch: x64
[21:07:20.822] ------------------------------
[21:07:20.822] Command-Line: 
[21:07:20.822] ------------------------------
[21:07:20.822] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:07:20.822] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:07:20.823] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[21:07:20.823] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[21:07:20.823] Runtime Type: net6
[21:07:20.889] ------------------------------
[21:07:20.890] Game Name: BloonsTD6
[21:07:20.890] Game Developer: Ninja Kiwi
[21:07:20.891] Unity Version: 6000.0.58f1
[21:07:20.892] Game Version: UNKNOWN
[21:07:20.892] ------------------------------

[21:07:21.291] Preferences Loaded!

[21:07:21.302] Loading UserLibs...
[21:07:21.305] 0 UserLibs loaded.

[21:07:21.305] Loading Plugins...
[21:07:21.309] 0 Plugins loaded.

[21:07:21.713] Loading Il2CppAssemblyGenerator...
[21:07:21.757] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[21:07:22.045] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[21:07:22.045] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[21:07:22.045] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[21:07:22.046] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[21:07:22.052] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[21:07:22.052] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[21:07:22.053] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.58
[21:07:22.053] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[21:07:22.053] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[21:07:22.054] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[21:07:22.054] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[21:07:22.054] [Il2CppAssemblyGenerator] Checking GameAssembly...
[21:07:22.193] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[21:07:22.194] Loading Mods...
[21:07:22.242] ------------------------------
[21:07:22.278] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[21:07:22.278] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[21:07:22.290] Melon Assembly loaded: '.\Mods\CardMonkey.dll'
[21:07:22.290] SHA256 Hash: '1B332E160A11A29E520A4AE7D4AF9C614829BB46D11765D2F13A01AD4DE3F179'
[21:07:22.291] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[21:07:22.292] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[21:07:22.297] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[21:07:22.297] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[21:07:22.373] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[21:07:22.374] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[21:07:22.379] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[21:07:22.379] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[21:07:22.946] ------------------------------
[21:07:22.946] BloonsTD6 Mod Helper v3.4.12
[21:07:22.946] by Gurrenm4 and Doombubbles
[21:07:22.947] Assembly: Btd6ModHelper.dll
[21:07:22.947] ------------------------------
[21:07:22.950] ------------------------------
[21:07:22.951] Ultimate Crosspathing v1.7.1
[21:07:22.951] by doombubbles
[21:07:22.951] Assembly: UltimateCrosspathing.dll
[21:07:22.951] ------------------------------
[21:07:22.953] ------------------------------
[21:07:22.953] Card Monkey v1.2.16
[21:07:22.954] by doombubbles
[21:07:22.954] Assembly: CardMonkey.dll
[21:07:22.954] ------------------------------
[21:07:22.956] ------------------------------
[21:07:22.956] Faster Forward v1.1.5
[21:07:22.956] by doombubbles
[21:07:22.957] Assembly: FasterForward.dll
[21:07:22.957] ------------------------------
[21:07:22.959] ------------------------------
[21:07:22.959] Powers in Shop v3.0.3
[21:07:22.959] by doombubbles
[21:07:22.959] Assembly: PowersInShop.dll
[21:07:22.959] ------------------------------
[21:07:22.961] ------------------------------
[21:07:22.962] Unlimited 5th Tiers + v1.1.9
[21:07:22.962] by doombubbles
[21:07:22.962] Assembly: Unlimited5thTiers.dll
[21:07:22.962] ------------------------------
[21:07:22.962] ------------------------------
[21:07:22.962] 6 Mods loaded.

[21:07:24.319] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[21:07:24.490] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
