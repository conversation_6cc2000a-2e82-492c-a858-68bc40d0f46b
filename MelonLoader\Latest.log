
[20:19:30.557] ------------------------------
[20:19:30.559] MelonLoader v0.7.1 Open-Beta
[20:19:30.560] OS: Windows 11
[20:19:30.560] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[20:19:30.560] ------------------------------
[20:19:30.561] Game Type: Il2cpp
[20:19:30.561] Game Arch: x64
[20:19:30.561] ------------------------------
[20:19:30.562] Command-Line: 
[20:19:30.562] ------------------------------
[20:19:30.562] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[20:19:30.562] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[20:19:30.563] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[20:19:30.563] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[20:19:30.563] Runtime Type: net6
[20:19:30.629] ------------------------------
[20:19:30.629] Game Name: BloonsTD6
[20:19:30.630] Game Developer: Ninja Kiwi
[20:19:30.632] Unity Version: 6000.0.52f1
[20:19:30.632] Game Version: 50.2
[20:19:30.632] ------------------------------

[20:19:31.052] Preferences Loaded!

[20:19:31.064] Loading UserLibs...
[20:19:31.066] 0 UserLibs loaded.

[20:19:31.067] Loading Plugins...
[20:19:31.071] 0 Plugins loaded.

[20:19:31.941] Loading Il2CppAssemblyGenerator...
[20:19:31.993] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[20:19:32.232] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[20:19:32.232] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[20:19:32.232] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[20:19:32.233] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[20:19:32.240] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[20:19:32.240] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[20:19:32.240] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[20:19:32.240] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[20:19:32.240] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[20:19:32.240] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[20:19:32.241] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[20:19:32.241] [Il2CppAssemblyGenerator] Checking GameAssembly...
[20:19:32.428] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[20:19:32.429] Loading Mods...
[20:19:32.484] ------------------------------
[20:19:32.529] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[20:19:32.529] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[20:19:32.533] Melon Assembly loaded: '.\Mods\EditPlayerData.dll'
[20:19:32.533] SHA256 Hash: 'D0AE1A904057FF46AF12EA6A6856E28954B9DE4191999802288DA1DD00C4483D'
[20:19:32.534] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[20:19:32.534] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[20:19:32.540] Melon Assembly loaded: '.\Mods\IndustrialFarmer.dll'
[20:19:32.540] SHA256 Hash: '55C6358F421E23428D41BA902A7B3DC9D22F626234AD9CC3BAE27C032087F2D3'
[20:19:32.541] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[20:19:32.541] SHA256 Hash: 'C23277E37C12E91D3C47EB6A2548626CCE3111679FC4770DDEA2C06B5B77C9E9'
[20:19:32.547] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[20:19:32.547] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[20:19:32.644] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[20:19:32.644] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[20:19:32.655] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[20:19:32.655] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[20:19:33.314] ------------------------------
[20:19:33.314] BloonsTD6 Mod Helper v3.4.12
[20:19:33.314] by Gurrenm4 and Doombubbles
[20:19:33.314] Assembly: Btd6ModHelper.dll
[20:19:33.315] ------------------------------
[20:19:33.318] ------------------------------
[20:19:33.318] Ultimate Crosspathing v1.7.1
[20:19:33.318] by doombubbles
[20:19:33.318] Assembly: UltimateCrosspathing.dll
[20:19:33.319] ------------------------------
[20:19:33.321] ------------------------------
[20:19:33.321] EditPlayerData v1.5.1
[20:19:33.321] by MaliciousFiles
[20:19:33.322] Assembly: EditPlayerData.dll
[20:19:33.322] ------------------------------
[20:19:33.324] ------------------------------
[20:19:33.324] Faster Forward v1.1.5
[20:19:33.324] by doombubbles
[20:19:33.325] Assembly: FasterForward.dll
[20:19:33.325] ------------------------------
[20:19:33.327] ------------------------------
[20:19:33.327] Industrial Farmer v1.0.15
[20:19:33.327] by doombubbles
[20:19:33.327] Assembly: IndustrialFarmer.dll
[20:19:33.328] ------------------------------
[20:19:33.331] ------------------------------
[20:19:33.331] Lightning Monkey Mod v1.0.0
[20:19:33.331] by You
[20:19:33.331] Assembly: LightningMonkey.dll
[20:19:33.331] ------------------------------
[20:19:33.334] ------------------------------
[20:19:33.334] Powers in Shop v3.0.3
[20:19:33.334] by doombubbles
[20:19:33.334] Assembly: PowersInShop.dll
[20:19:33.334] ------------------------------
[20:19:33.337] ------------------------------
[20:19:33.337] Unlimited 5th Tiers + v1.1.9
[20:19:33.337] by doombubbles
[20:19:33.337] Assembly: Unlimited5thTiers.dll
[20:19:33.337] ------------------------------
[20:19:33.337] ------------------------------
[20:19:33.337] 8 Mods loaded.

[20:19:34.567] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[20:19:34.741] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[20:19:34.772] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[20:19:34.783] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[20:19:34.798] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[20:19:34.800] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[20:19:34.900] [Il2CppInterop] Method Void SetNumColumns(Int32, Int32[]) on type EditPlayerData.UI.ModHelperTable has unsupported parameter Int32[] colFlex of type System.Int32[]
[20:19:34.902] [Il2CppInterop] Method Void SetSetting(EditPlayerData.UI.PlayerDataSetting) on type EditPlayerData.UI.PlayerDataSettingDisplay has unsupported parameter EditPlayerData.UI.PlayerDataSetting setting of type EditPlayerData.UI.PlayerDataSetting
[20:19:35.308] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[20:19:35.836] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[20:19:36.516] [EditPlayerData] Melon Assembly loaded: 'C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Windows.Forms.dll'
[20:19:36.516] [EditPlayerData] SHA256 Hash: 'F8EE5BFDC1FE7508773CEB997F6C78FC8CF8CDBAE11F0DEB15E78AA94BEBFED7'
[20:19:36.518] [EditPlayerData] EditPlayerData loaded!
[20:19:36.653] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[20:19:37.363] [BloonsTD6_Mod_Helper] AlchemistLoader finished loading bytes
[20:19:37.468] [BloonsTD6_Mod_Helper] BananaFarmLoader finished loading bytes
[20:19:37.512] [BloonsTD6_Mod_Helper] BananaFarmerProLoader finished loading bytes
[20:19:37.722] [BloonsTD6_Mod_Helper] BeastHandlerLoader finished loading bytes
[20:19:37.826] [BloonsTD6_Mod_Helper] BombShooterLoader finished loading bytes
[20:19:37.894] [BloonsTD6_Mod_Helper] BoomerangMonkeyLoader finished loading bytes
[20:19:37.973] [BloonsTD6_Mod_Helper] DartMonkeyLoader finished loading bytes
[20:19:38.172] [BloonsTD6_Mod_Helper] DartlingGunnerLoader finished loading bytes
[20:19:38.279] [BloonsTD6_Mod_Helper] DesperadoLoader finished loading bytes
[20:19:38.427] [BloonsTD6_Mod_Helper] DruidLoader finished loading bytes
[20:19:38.618] [BloonsTD6_Mod_Helper] EngineerMonkeyLoader finished loading bytes
[20:19:38.691] [BloonsTD6_Mod_Helper] GlueGunnerLoader finished loading bytes
[20:19:38.857] [BloonsTD6_Mod_Helper] HeliPilotLoader finished loading bytes
[20:19:38.958] [BloonsTD6_Mod_Helper] IceMonkeyLoader finished loading bytes
[20:19:39.247] [BloonsTD6_Mod_Helper] MermonkeyLoader finished loading bytes
[20:19:39.419] [BloonsTD6_Mod_Helper] MonkeyAceLoader finished loading bytes
[20:19:39.618] [BloonsTD6_Mod_Helper] MonkeyBuccaneerLoader finished loading bytes
[20:19:39.737] [BloonsTD6_Mod_Helper] MonkeySubLoader finished loading bytes
[20:19:39.849] [BloonsTD6_Mod_Helper] MonkeyVillageLoader finished loading bytes
[20:19:40.024] [BloonsTD6_Mod_Helper] MortarMonkeyLoader finished loading bytes
[20:19:40.125] [BloonsTD6_Mod_Helper] NinjaMonkeyLoader finished loading bytes
[20:19:40.220] [BloonsTD6_Mod_Helper] SniperMonkeyLoader finished loading bytes
[20:19:40.371] [BloonsTD6_Mod_Helper] SpikeFactoryLoader finished loading bytes
[20:19:40.401] [BloonsTD6_Mod_Helper] SuperMonkeyBeaconLoader finished loading bytes
[20:19:40.832] [BloonsTD6_Mod_Helper] SuperMonkeyLoader finished loading bytes
[20:19:40.897] [BloonsTD6_Mod_Helper] TackShooterLoader finished loading bytes
[20:19:41.133] [BloonsTD6_Mod_Helper] WizardMonkeyLoader finished loading bytes
[20:19:42.998] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 356 mods over 7.1 seconds
[20:19:46.900] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[20:19:46.905] [BloonsTD6_Mod_Helper] Registering ModContent for Ultimate Crosspathing...
[20:19:46.971] [Ultimate_Crosspathing] Finished loading DartMonkeys!
[20:19:47.095] [Ultimate_Crosspathing] Finished loading BoomerangMonkeys!
[20:19:47.128] [Ultimate_Crosspathing] Finished loading BombShooters!
[20:19:47.169] [Ultimate_Crosspathing] Finished loading TackShooters!
[20:19:47.214] [Ultimate_Crosspathing] Finished loading IceMonkeys!
[20:19:47.251] [Ultimate_Crosspathing] Finished loading GlueGunners!
[20:19:47.294] [Ultimate_Crosspathing] Finished loading Desperados!
[20:19:47.339] [Ultimate_Crosspathing] Finished loading SniperMonkeys!
[20:19:47.379] [Ultimate_Crosspathing] Finished loading MonkeySubs!
[20:19:47.438] [Ultimate_Crosspathing] Finished loading MonkeyBuccaneers!
[20:19:47.492] [Ultimate_Crosspathing] Finished loading MonkeyAces!
[20:19:47.543] [Ultimate_Crosspathing] Finished loading HeliPilots!
[20:19:47.593] [Ultimate_Crosspathing] Finished loading MortarMonkeys!
[20:19:47.641] [Ultimate_Crosspathing] Finished loading DartlingGunners!
[20:19:47.674] [Ultimate_Crosspathing] Finished loading WizardMonkeys!
[20:19:47.774] [Ultimate_Crosspathing] Finished loading SuperMonkeys!
[20:19:47.816] [Ultimate_Crosspathing] Finished loading NinjaMonkeys!
[20:19:47.873] [Ultimate_Crosspathing] Finished loading Alchemists!
[20:19:47.918] [Ultimate_Crosspathing] Finished loading Druids!
[20:19:47.991] [Ultimate_Crosspathing] Finished loading Mermonkeys!
[20:19:48.046] [Ultimate_Crosspathing] Finished loading BananaFarms!
[20:19:48.096] [Ultimate_Crosspathing] Finished loading SpikeFactorys!
[20:19:48.166] [Ultimate_Crosspathing] Finished loading MonkeyVillages!
[20:19:48.200] [Ultimate_Crosspathing] Finished loading EngineerMonkeys!
[20:19:48.252] [Ultimate_Crosspathing] Finished loading BeastHandlers!
[20:19:48.276] [Ultimate_Crosspathing] Finished loading BananaFarmerPros!
[20:19:48.288] [Ultimate_Crosspathing] Finished loading SuperMonkeyBeacons!
[20:19:48.288] [BloonsTD6_Mod_Helper] Registering ModContent for EditPlayerData...
[20:19:48.289] [BloonsTD6_Mod_Helper] Registering ModContent for Industrial Farmer...
[20:19:48.349] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[20:19:48.511] [BloonsTD6_Mod_Helper] Registering ModContent for Powers in Shop...
[20:19:48.572] [BloonsTD6_Mod_Helper] Registering ModContent for Unlimited 5th Tiers +...
