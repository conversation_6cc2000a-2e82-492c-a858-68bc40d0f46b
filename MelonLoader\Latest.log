
[08:20:21.359] ------------------------------
[08:20:21.361] MelonLoader v0.7.1 Open-Beta
[08:20:21.362] OS: Windows 11
[08:20:21.362] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[08:20:21.362] ------------------------------
[08:20:21.363] Game Type: Il2cpp
[08:20:21.363] Game Arch: x64
[08:20:21.363] ------------------------------
[08:20:21.363] Command-Line: 
[08:20:21.364] ------------------------------
[08:20:21.364] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[08:20:21.364] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[08:20:21.364] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[08:20:21.364] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[08:20:21.364] Runtime Type: net6
[08:20:21.427] ------------------------------
[08:20:21.427] Game Name: BloonsTD6
[08:20:21.427] Game Developer: Ninja Kiwi
[08:20:21.429] Unity Version: 6000.0.52f1
[08:20:21.429] Game Version: 50.2
[08:20:21.429] ------------------------------

[08:20:21.815] Preferences Loaded!

[08:20:21.826] Loading UserLibs...
[08:20:21.828] 0 UserLibs loaded.

[08:20:21.829] Loading Plugins...
[08:20:21.833] 0 Plugins loaded.

[08:20:22.210] Loading Il2CppAssemblyGenerator...
[08:20:22.247] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[08:20:22.398] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[08:20:22.398] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[08:20:22.399] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[08:20:22.399] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[08:20:22.404] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[08:20:22.404] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[08:20:22.404] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[08:20:22.405] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[08:20:22.405] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[08:20:22.405] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[08:20:22.405] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[08:20:22.405] [Il2CppAssemblyGenerator] Checking GameAssembly...
[08:20:22.543] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[08:20:22.544] Loading Mods...
[08:20:22.586] ------------------------------
[08:20:22.621] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[08:20:22.622] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[08:20:22.624] Melon Assembly loaded: '.\Mods\EditPlayerData.dll'
[08:20:22.624] SHA256 Hash: 'D0AE1A904057FF46AF12EA6A6856E28954B9DE4191999802288DA1DD00C4483D'
[08:20:22.626] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[08:20:22.626] SHA256 Hash: 'C23277E37C12E91D3C47EB6A2548626CCE3111679FC4770DDEA2C06B5B77C9E9'
[08:20:22.630] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[08:20:22.630] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[08:20:22.716] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[08:20:22.716] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'

[08:20:23.255] ------------------------------
[08:20:23.255] BloonsTD6 Mod Helper v3.4.12
[08:20:23.255] by Gurrenm4 and Doombubbles
[08:20:23.255] Assembly: Btd6ModHelper.dll
[08:20:23.256] ------------------------------
[08:20:23.259] ------------------------------
[08:20:23.259] Ultimate Crosspathing v1.7.1
[08:20:23.259] by doombubbles
[08:20:23.259] Assembly: UltimateCrosspathing.dll
[08:20:23.259] ------------------------------
[08:20:23.261] ------------------------------
[08:20:23.261] EditPlayerData v1.5.1
[08:20:23.261] by MaliciousFiles
[08:20:23.261] Assembly: EditPlayerData.dll
[08:20:23.261] ------------------------------
[08:20:23.263] ------------------------------
[08:20:23.263] Lightning Monkey Mod v1.0.0
[08:20:23.263] by You
[08:20:23.264] Assembly: LightningMonkey.dll
[08:20:23.264] ------------------------------
[08:20:23.266] ------------------------------
[08:20:23.266] Powers in Shop v3.0.3
[08:20:23.266] by doombubbles
[08:20:23.266] Assembly: PowersInShop.dll
[08:20:23.266] ------------------------------
[08:20:23.266] ------------------------------
[08:20:23.266] 5 Mods loaded.

[08:20:24.205] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[08:20:24.375] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[08:20:24.398] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[08:20:24.414] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[08:20:24.426] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[08:20:24.428] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[08:20:24.503] [Il2CppInterop] Method Void SetNumColumns(Int32, Int32[]) on type EditPlayerData.UI.ModHelperTable has unsupported parameter Int32[] colFlex of type System.Int32[]
[08:20:24.505] [Il2CppInterop] Method Void SetSetting(EditPlayerData.UI.PlayerDataSetting) on type EditPlayerData.UI.PlayerDataSettingDisplay has unsupported parameter EditPlayerData.UI.PlayerDataSetting setting of type EditPlayerData.UI.PlayerDataSetting
[08:20:24.854] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[08:20:25.309] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[08:20:25.540] [EditPlayerData] Melon Assembly loaded: 'C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Windows.Forms.dll'
[08:20:25.540] [EditPlayerData] SHA256 Hash: 'F8EE5BFDC1FE7508773CEB997F6C78FC8CF8CDBAE11F0DEB15E78AA94BEBFED7'
[08:20:25.541] [EditPlayerData] EditPlayerData loaded!
[08:20:25.705] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[08:20:26.100] [BloonsTD6_Mod_Helper] AlchemistLoader finished loading bytes
[08:20:26.196] [BloonsTD6_Mod_Helper] BananaFarmLoader finished loading bytes
[08:20:26.235] [BloonsTD6_Mod_Helper] BananaFarmerProLoader finished loading bytes
[08:20:26.405] [BloonsTD6_Mod_Helper] BeastHandlerLoader finished loading bytes
[08:20:26.509] [BloonsTD6_Mod_Helper] BombShooterLoader finished loading bytes
[08:20:26.574] [BloonsTD6_Mod_Helper] BoomerangMonkeyLoader finished loading bytes
[08:20:26.665] [BloonsTD6_Mod_Helper] DartMonkeyLoader finished loading bytes
[08:20:26.835] [BloonsTD6_Mod_Helper] DartlingGunnerLoader finished loading bytes
[08:20:26.927] [BloonsTD6_Mod_Helper] DesperadoLoader finished loading bytes
[08:20:27.058] [BloonsTD6_Mod_Helper] DruidLoader finished loading bytes
[08:20:27.248] [BloonsTD6_Mod_Helper] EngineerMonkeyLoader finished loading bytes
[08:20:27.332] [BloonsTD6_Mod_Helper] GlueGunnerLoader finished loading bytes
[08:20:27.521] [BloonsTD6_Mod_Helper] HeliPilotLoader finished loading bytes
[08:20:27.666] [BloonsTD6_Mod_Helper] IceMonkeyLoader finished loading bytes
[08:20:27.895] [BloonsTD6_Mod_Helper] MermonkeyLoader finished loading bytes
[08:20:28.005] [BloonsTD6_Mod_Helper] MonkeyAceLoader finished loading bytes
[08:20:28.239] [BloonsTD6_Mod_Helper] MonkeyBuccaneerLoader finished loading bytes
[08:20:28.334] [BloonsTD6_Mod_Helper] MonkeySubLoader finished loading bytes
[08:20:28.445] [BloonsTD6_Mod_Helper] MonkeyVillageLoader finished loading bytes
[08:20:28.628] [BloonsTD6_Mod_Helper] MortarMonkeyLoader finished loading bytes
[08:20:28.705] [BloonsTD6_Mod_Helper] NinjaMonkeyLoader finished loading bytes
[08:20:28.753] [BloonsTD6_Mod_Helper] SniperMonkeyLoader finished loading bytes
[08:20:28.844] [BloonsTD6_Mod_Helper] SpikeFactoryLoader finished loading bytes
[08:20:28.875] [BloonsTD6_Mod_Helper] SuperMonkeyBeaconLoader finished loading bytes
[08:20:29.166] [BloonsTD6_Mod_Helper] SuperMonkeyLoader finished loading bytes
[08:20:29.226] [BloonsTD6_Mod_Helper] TackShooterLoader finished loading bytes
[08:20:29.413] [BloonsTD6_Mod_Helper] WizardMonkeyLoader finished loading bytes
[08:20:32.451] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 377 mods over 7.1 seconds
[08:20:34.744] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[08:20:34.747] [BloonsTD6_Mod_Helper] Registering ModContent for Ultimate Crosspathing...
[08:20:34.804] [Ultimate_Crosspathing] Finished loading DartMonkeys!
[08:20:34.829] [Ultimate_Crosspathing] Finished loading BoomerangMonkeys!
[08:20:34.884] [Ultimate_Crosspathing] Finished loading BombShooters!
[08:20:34.956] [Ultimate_Crosspathing] Finished loading TackShooters!
[08:20:35.000] [Ultimate_Crosspathing] Finished loading IceMonkeys!
[08:20:35.040] [Ultimate_Crosspathing] Finished loading GlueGunners!
[08:20:35.087] [Ultimate_Crosspathing] Finished loading Desperados!
[08:20:35.129] [Ultimate_Crosspathing] Finished loading SniperMonkeys!
[08:20:35.262] [Ultimate_Crosspathing] Finished loading MonkeySubs!
[08:20:35.323] [Ultimate_Crosspathing] Finished loading MonkeyBuccaneers!
[08:20:35.354] [Ultimate_Crosspathing] Finished loading MonkeyAces!
[08:20:35.414] [Ultimate_Crosspathing] Finished loading HeliPilots!
[08:20:35.459] [Ultimate_Crosspathing] Finished loading MortarMonkeys!
[08:20:35.517] [Ultimate_Crosspathing] Finished loading DartlingGunners!
[08:20:35.560] [Ultimate_Crosspathing] Finished loading WizardMonkeys!
[08:20:35.649] [Ultimate_Crosspathing] Finished loading SuperMonkeys!
[08:20:35.692] [Ultimate_Crosspathing] Finished loading NinjaMonkeys!
[08:20:35.746] [Ultimate_Crosspathing] Finished loading Alchemists!
[08:20:35.792] [Ultimate_Crosspathing] Finished loading Druids!
[08:20:35.856] [Ultimate_Crosspathing] Finished loading Mermonkeys!
[08:20:35.911] [Ultimate_Crosspathing] Finished loading BananaFarms!
[08:20:35.967] [Ultimate_Crosspathing] Finished loading SpikeFactorys!
[08:20:36.012] [Ultimate_Crosspathing] Finished loading MonkeyVillages!
[08:20:36.045] [Ultimate_Crosspathing] Finished loading EngineerMonkeys!
[08:20:36.093] [Ultimate_Crosspathing] Finished loading BeastHandlers!
[08:20:36.120] [Ultimate_Crosspathing] Finished loading BananaFarmerPros!
[08:20:36.133] [Ultimate_Crosspathing] Finished loading SuperMonkeyBeacons!
[08:20:36.134] [BloonsTD6_Mod_Helper] Registering ModContent for EditPlayerData...
[08:20:36.134] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[08:20:36.301] [BloonsTD6_Mod_Helper] Registering ModContent for Powers in Shop...
[08:28:00.909] Preferences Saved!
