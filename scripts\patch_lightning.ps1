$ErrorActionPreference = 'Stop'
$path = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey\Towers\LightningMonkey.cs'
if (-not (Test-Path -LiteralPath $path)) {
  Write-Error "File not found: $path"
}
$content = Get-Content -Raw -LiteralPath $path

# Ensure quoted strings for DisplayName and Description
$content = [regex]::Replace($content, 'public\s+override\s+string\s+DisplayName\s*=>\s*[^;]+;', 'public override string DisplayName => "Lightning Monkey";')
$content = [regex]::Replace($content, 'public\s+override\s+string\s+Description\s*=>\s*[^;]+;', 'public override string Description => "Harnesses crackling lightning that chains through bloons.";')

# Ensure Druid-200 tower ID is passed as a string
$content = [regex]::Replace($content, 'GetTowerFromId\([^)]*\)', 'GetTowerFromId("Druid-200")')

Set-Content -Encoding UTF8 -LiteralPath $path -Value $content

Write-Host 'Patched lines preview:'
(Get-Content -Raw -LiteralPath $path) |
  Select-String -Pattern 'DisplayName|Description|GetTowerFromId\("Druid-200"\)' -CaseSensitive | ForEach-Object { $_.Line }
