# Setup Lightning Monkey full mod project and build
$ErrorActionPreference = 'Stop'

# Paths
$docs = [Environment]::GetFolderPath('MyDocuments')
$root = Join-Path $docs 'BTD6 Mod Sources'
if (-not (Test-Path $root)) { New-Item -ItemType Directory -Path $root -Force | Out-Null }
$proj = Join-Path $root 'LightningMonkey'
$srcTowers = Join-Path $proj 'Towers'
$srcUpg = Join-Path $proj 'Towers\Upgrades'
$srcRes = Join-Path $proj 'Resources'
$srcVS = Join-Path $proj '.vscode'

# Create folders
New-Item -ItemType Directory -Path $proj -Force | Out-Null
New-Item -ItemType Directory -Path $srcTowers -Force | Out-Null
New-Item -ItemType Directory -Path $srcUpg -Force | Out-Null
New-Item -ItemType Directory -Path $srcRes -Force | Out-Null
New-Item -ItemType Directory -Path $srcVS -Force | Out-Null

# Files content (mostly single-line to keep script short)
$csproj = '<Project Sdk="Microsoft.NET.Sdk"><PropertyGroup><TargetFramework>net48</TargetFramework><LangVersion>latest</LangVersion><Nullable>disable</Nullable><AssemblyName>LightningMonkey</AssemblyName><RootNamespace>LightningMonkey</RootNamespace></PropertyGroup><Import Project="..\btd6.targets" /><ItemGroup><EmbeddedResource Include="Resources\**\*.png" /></ItemGroup><Target Name="AfterBuild" AfterTargets="Build"><Copy SourceFiles="$(TargetDir)$(TargetFileName)" DestinationFolder="C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\Mods" OverWriteReadOnlyFiles="true" /></Target></Project>'
$main = 'using BTD_Mod_Helper; using MelonLoader; [assembly: MelonInfo(typeof(LightningMonkey.Main), "Lightning Monkey Mod", "1.0.0", "You")] [assembly: MelonGame("Ninja Kiwi", "BloonsTD6")] namespace LightningMonkey { public class Main : BloonsTD6Mod { } }'
$tower = 'using BTD_Mod_Helper.Api.Towers; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers { public class LightningMonkeyTower : ModTower { public override string BaseTower => TowerType.DartMonkey; public override TowerSet TowerSet => TowerSet.Magic; public override string DisplayName => "Lightning Monkey"; public override string Description => "Channels crackling bolts that chain through bloons."; public override int Cost => 550; public override string Icon => "LightningMonkey-Icon"; public override string Portrait => "LightningMonkey-Portrait"; public override int TopPathUpgrades => 5; public override int MiddlePathUpgrades => 5; public override int BottomPathUpgrades => 5; public override void ModifyBaseTowerModel(TowerModel m){ m.range += 8f; var atk=m.GetAttackModel(); var druid020 = m.GetTowerFromId("Druid-020").GetAttackModel(); atk.weapons[0].projectile = druid020.weapons[0].projectile.Duplicate(); atk.weapons[0].Rate *= 0.95f; var p=atk.weapons[0].projectile; p.pierce += 1; p.GetDamageModel().damage += 1; } } }'

$upgrades = @(
  @{ name = 'Top_T1_ChargedBolts'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class ChargedBolts : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Charged Bolts"; public override string Description=>"+1 damage, +1 pierce."; public override int Path=>TOP; public override int Tier=>1; public override int Cost=>350; public override void ApplyUpgrade(TowerModel m){ var w=m.GetAttackModel().weapons[0]; w.projectile.GetDamageModel().damage+=1; w.projectile.pierce+=1; } } }' },
  @{ name = 'Top_T2_Supercharged'; content = 'using BTD_Mod_Helper.Api.Upgrades; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class Supercharged : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Supercharged"; public override string Description=>"Attacks 15% faster."; public override int Path=>TOP; public override int Tier=>2; public override int Cost=>850; public override void ApplyUpgrade(TowerModel m){ m.GetAttackModel().weapons[0].Rate*=0.85f; } } }' },
  @{ name = 'Top_T3_Thunderhead'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class Thunderhead : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Thunderhead"; public override string Description=>"+1 damage, +2 pierce."; public override int Path=>TOP; public override int Tier=>3; public override int Cost=>1900; public override void ApplyUpgrade(TowerModel m){ var w=m.GetAttackModel().weapons[0]; w.projectile.GetDamageModel().damage+=1; w.projectile.pierce+=2; } } }' },
  @{ name = 'Top_T4_TempestStrike'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class TempestStrike : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Tempest Strike"; public override string Description=>"+2 damage, +2 pierce, 10% faster."; public override int Path=>TOP; public override int Tier=>4; public override int Cost=>6000; public override void ApplyUpgrade(TowerModel m){ var w=m.GetAttackModel().weapons[0]; w.Rate*=0.9f; w.projectile.GetDamageModel().damage+=2; w.projectile.pierce+=2; } } }' },
  @{ name = 'Top_T5_Stormlord'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class Stormlord : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Stormlord"; public override string Description=>"+3 damage, +4 pierce, 20% faster."; public override int Path=>TOP; public override int Tier=>5; public override int Cost=>28000; public override void ApplyUpgrade(TowerModel m){ var w=m.GetAttackModel().weapons[0]; w.Rate*=0.8f; w.projectile.GetDamageModel().damage+=3; w.projectile.pierce+=4; } } }' },
  @{ name = 'Mid_T1_QuickChanneling'; content = 'using BTD_Mod_Helper.Api.Upgrades; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class QuickChanneling : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Quick Channeling"; public override string Description=>"10% faster."; public override int Path=>MIDDLE; public override int Tier=>1; public override int Cost=>300; public override void ApplyUpgrade(TowerModel m){ m.GetAttackModel().weapons[0].Rate*=0.9f; } } }' },
  @{ name = 'Mid_T2_SurgeCasting'; content = 'using BTD_Mod_Helper.Api.Upgrades; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class SurgeCasting : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Surge Casting"; public override string Description=>"Another 15% faster."; public override int Path=>MIDDLE; public override int Tier=>2; public override int Cost=>900; public override void ApplyUpgrade(TowerModel m){ m.GetAttackModel().weapons[0].Rate*=0.85f; } } }' },
  @{ name = 'Mid_T3_ForkedLightning'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class ForkedLightning : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Forked Lightning"; public override string Description=>"Fires 3 bolts in a spread."; public override int Path=>MIDDLE; public override int Tier=>3; public override int Cost=>2400; public override void ApplyUpgrade(TowerModel m){ var w=m.GetAttackModel().weapons[0]; var refW=m.GetTowerFromId("DartMonkey-203").GetAttackModel().weapons[0]; w.emission = refW.emission.Duplicate(); } } }' },
  @{ name = 'Mid_T4_ChainMastery'; content = 'using BTD_Mod_Helper.Api.Upgrades; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class ChainMastery : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Chain Mastery"; public override string Description=>"+2 pierce, 10% faster."; public override int Path=>MIDDLE; public override int Tier=>4; public override int Cost=>7000; public override void ApplyUpgrade(TowerModel m){ var w=m.GetAttackModel().weapons[0]; w.Rate*=0.9f; w.projectile.pierce+=2; } } }' },
  @{ name = 'Mid_T5_MaelstromConduit'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class MaelstromConduit : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Maelstrom Conduit"; public override string Description=>"+2 damage, +3 pierce, 25% faster."; public override int Path=>MIDDLE; public override int Tier=>5; public override int Cost=>30000; public override void ApplyUpgrade(TowerModel m){ var w=m.GetAttackModel().weapons[0]; w.Rate*=0.75f; w.projectile.GetDamageModel().damage+=2; w.projectile.pierce+=3; } } }' },
  @{ name = 'Bot_T1_StaticField'; content = 'using BTD_Mod_Helper.Api.Upgrades; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class StaticField : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Static Field"; public override string Description=>"+8 range."; public override int Path=>BOTTOM; public override int Tier=>1; public override int Cost=>250; public override void ApplyUpgrade(TowerModel m){ m.range += 8f; } } }' },
  @{ name = 'Bot_T2_IonFocus'; content = 'using BTD_Mod_Helper.Api.Upgrades; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class IonFocus : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Ion Focus"; public override string Description=>"10% faster."; public override int Path=>BOTTOM; public override int Tier=>2; public override int Cost=>700; public override void ApplyUpgrade(TowerModel m){ m.GetAttackModel().weapons[0].Rate*=0.9f; } } }' },
  @{ name = 'Bot_T3_SkyReach'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class SkyReach : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Sky Reach"; public override string Description=>"+10 range and +1 damage."; public override int Path=>BOTTOM; public override int Tier=>3; public override int Cost=>1700; public override void ApplyUpgrade(TowerModel m){ m.range += 10f; m.GetAttackModel().weapons[0].projectile.GetDamageModel().damage += 1; } } }' },
  @{ name = 'Bot_T4_StormSight'; content = 'using BTD_Mod_Helper.Api.Upgrades; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class StormSight : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Storm Sight"; public override string Description=>"+8 range, 10% faster."; public override int Path=>BOTTOM; public override int Tier=>4; public override int Cost=>5200; public override void ApplyUpgrade(TowerModel m){ m.range += 8f; m.GetAttackModel().weapons[0].Rate *= 0.9f; } } }' },
  @{ name = 'Bot_T5_ZeusVision'; content = 'using BTD_Mod_Helper.Api.Upgrades; using BTD_Mod_Helper.Extensions; using Il2CppAssets.Scripts.Models.Towers; namespace LightningMonkey.Towers.Upgrades { public class ZeusVision : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> { public override string DisplayName=>"Zeus Vision"; public override string Description=>"+12 range, +2 damage."; public override int Path=>BOTTOM; public override int Tier=>5; public override int Cost=>26000; public override void ApplyUpgrade(TowerModel m){ m.range += 12f; m.GetAttackModel().weapons[0].projectile.GetDamageModel().damage += 2; } } }' }
)

$tasksJson = '{"version":"2.0.0","tasks":[{"label":"Build LightningMonkey (Release -> Mods)","type":"shell","command":"dotnet build -c Release","options":{"cwd":"${workspaceFolder}"},"problemMatcher":"$msCompile"}]}'

# Write files
Set-Content -Encoding UTF8 -Path (Join-Path $proj 'LightningMonkey.csproj') -Value $csproj
Set-Content -Encoding UTF8 -Path (Join-Path $proj 'Main.cs') -Value $main
Set-Content -Encoding UTF8 -Path (Join-Path $srcTowers 'LightningMonkey.cs') -Value $tower
foreach ($u in $upgrades) { Set-Content -Encoding UTF8 -Path (Join-Path $srcUpg ("{0}.cs" -f $u.name)) -Value $u.content }
Set-Content -Encoding UTF8 -Path (Join-Path $srcVS 'tasks.json') -Value $tasksJson

Write-Host "Created project at: $proj"

# Build if btd6.targets exists
$targets = Join-Path $root 'btd6.targets'
if (Test-Path $targets) {
  Write-Host "btd6.targets found - building Release..."
  Push-Location $proj
  try {
    dotnet build -c Release | Write-Host
  } catch {
    Write-Host "Build failed: $($_.Exception.Message)"
  } finally {
    Pop-Location
  }
} else {
  Write-Host ("WARNING: btd6.targets missing. Launch BTD6 once (with Mod Helper) to generate it, then run the VS Code task or run: dotnet build -c Release in " + $proj)
}

