@echo off
echo Creating <PERSON> Monkey with staff and chain lightning upgrade...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"
set "CHAIN_UPGRADE=%PROJ_DIR%\Towers\Upgrades\Mid_T3_ChainLightning.cs"

echo Writing clean Lightning Monkey tower...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Weapons;
echo using Il2CppAssets.Scripts.Unity;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.DartMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "A normal monkey wielding a mystical lightning staff! Shoots powerful lightning bolts that can chain between nearby bloons.";
echo     public override int Cost =^> 650;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel towerModel^) {
echo       towerModel.range += 15f;
echo.
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var weaponModel = attackModel.weapons[0];
echo         weaponModel.Rate = 0.9f;
echo.
echo         var projectile = weaponModel.projectile;
echo         projectile.pierce = 4f;
echo         projectile.GetDamageModel^(^).damage = 3f;
echo         projectile.scale = 1.3f;
echo       }
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Writing Chain Lightning upgrade...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class Mid_T3_ChainLightning : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 3;
echo     public override int Cost =^> 1200;
echo.
echo     public override string DisplayName =^> "Chain Lightning";
echo     public override string Description =^> "Lightning bolts now chain between nearby bloons, creating devastating electrical arcs that jump from target to target!";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo.
echo         // Create chain projectile
echo         var chainProjectile = projectile.Duplicate^(^);
echo         chainProjectile.pierce = 2f;
echo         chainProjectile.GetDamageModel^(^).damage = 2f;
echo         chainProjectile.scale = 0.8f;
echo.
echo         // Add chain behavior - creates new projectile on contact
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo           "ChainLightning",
echo           chainProjectile,
echo           new SingleEmissionModel^("", null^),
echo           true, false, false
echo         ^)^);
echo.
echo         // Increase main projectile stats
echo         projectile.pierce += 2f;
echo         projectile.GetDamageModel^(^).damage += 1f;
echo       }
echo     }
echo   }
echo }
) > "%CHAIN_UPGRADE%"

echo Building Lightning Monkey with Chain Lightning...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey with Chain Lightning!
    echo ========================================
    echo.
    echo FEATURES:
    echo - Normal monkey base ^(Dart Monkey^) with staff weapon
    echo - Enhanced range +15 ^(staff reach^)
    echo - 3 damage lightning bolts
    echo - 4 pierce base
    echo - 1.3x larger projectiles
    echo - 0.9s attack rate
    echo.
    echo CHAIN LIGHTNING UPGRADE ^(Middle T3^):
    echo - Lightning chains between nearby bloons!
    echo - Creates electrical arcs that jump targets
    echo - Chain projectiles: 2 damage, 2 pierce
    echo - Main bolt: +1 damage, +2 pierce
    echo - Cost: 1200
    echo.
    echo Launch BTD6 to test! ^(Magic category, 650 cost^)
    echo Upgrade to Chain Lightning for amazing effects!
) else (
    echo Build failed. Check errors above.
)
pause
