@echo off
setlocal
cd /d "%~dp0"

echo ===== Verifying Mod Helper / MelonLoader setup =====

if exist "Mods" (
  echo - Listing Mod Helper DLLs in Mods\
  dir "Mods\BloonsTD6*Helper*.dll" /b /s 2>nul
) else (
  echo Mods folder not found here. Current dir: %cd%
)

if exist "MelonLoader\Latest.log" (
  echo.
  echo - Extracting versions from MelonLoader\Latest.log
  findstr /R /C:"MelonLoader v" /C:"BTD6 Mod Helper" "MelonLoader\Latest.log"
) else (
  echo MelonLoader\Latest.log not found. Launch the game once to generate it.
)

echo.
echo - Recommended next steps if Mod Menu is missing:
echo   1) Re-run MelonLoader installer on BloonsTD6.exe
echo   2) Replace Mods\BloonsTD6 Mod Helper*.dll with the latest release (keep only ONE copy)

echo Done.
endlocal
pause
