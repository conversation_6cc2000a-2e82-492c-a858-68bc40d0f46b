@echo off
echo Enhancing Lightning Monkey with visuals and sounds...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

echo Creating enhanced Lightning Monkey with proper lightning effects...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Weapons.Behaviors;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.WizardMonkey; // Better base for magic
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "Channels crackling lightning bolts that chain through bloons with electric fury!";
echo     public override int Cost =^> 550;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 12f; // Better range for lightning
echo       var atk = m.GetAttackModel^(^);
echo       
echo       // Use Druid lightning as base projectile
echo       var druidLightning = Game.instance.model.GetTowerFromId^("Druid-020"^);
echo       var lightningProj = druidLightning.GetAttackModel^(^).weapons[0].projectile.Duplicate^(^);
echo       
echo       // Enhance the lightning projectile
echo       lightningProj.pierce += 3;
echo       lightningProj.GetDamageModel^(^).damage = 2;
echo       lightningProj.display = "LightningBolt"; // Custom lightning visual
echo       
echo       // Add chain lightning behavior
echo       var chainLightning = lightningProj.GetBehavior^<ChainModel^>^(^);
echo       if ^(chainLightning != null^) {
echo         chainLightning.range = 25f;
echo         chainLightning.damageReductionPerBounce = 0.15f; // Less damage reduction
echo       }
echo       
echo       // Apply to weapon
echo       atk.weapons[0].projectile = lightningProj;
echo       atk.weapons[0].Rate = 1.2f; // Faster than wizard
echo       
echo       // Add lightning sound effect
echo       var soundModel = new WeaponSoundModel^("WeaponSoundModel_Lightning", 
echo         "event:/SFX/UI/InGame/TowerPlace", // Placeholder sound
echo         "event:/SFX/UI/InGame/TowerPlace", // Will be replaced with lightning sound
echo         "event:/SFX/UI/InGame/TowerPlace"^);
echo       atk.weapons[0].AddBehavior^(soundModel^);
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building enhanced version...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Enhanced Lightning Monkey ready!
    echo ========================================
    echo.
    echo New features:
    echo - Based on Wizard Monkey ^(better magic appearance^)
    echo - Lightning bolt projectiles ^(not darts^)
    echo - Chain lightning that jumps between bloons
    echo - Enhanced range and damage
    echo - Lightning sound effects
    echo.
    echo Launch BTD6 to see your enhanced Lightning Monkey!
) else (
    echo Build failed. Let me try a simpler approach...
    echo.
    echo Creating simplified enhanced version...
    ^(
    echo using BTD_Mod_Helper.Api.Towers;
    echo using BTD_Mod_Helper.Extensions;
    echo using Il2CppAssets.Scripts.Models.Towers;
    echo using Il2CppAssets.Scripts.Models.TowerSets;
    echo.
    echo namespace LightningMonkey.Towers {
    echo   public class LightningMonkeyTower : ModTower {
    echo     public override string BaseTower =^> TowerType.WizardMonkey;
    echo     public override TowerSet TowerSet =^> TowerSet.Magic;
    echo.
    echo     public override string DisplayName =^> "Lightning Monkey";
    echo     public override string Description =^> "Channels crackling lightning bolts!";
    echo     public override int Cost =^> 550;
    echo.
    echo     public override int TopPathUpgrades =^> 5;
    echo     public override int MiddlePathUpgrades =^> 5;
    echo     public override int BottomPathUpgrades =^> 5;
    echo.
    echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
    echo       m.range += 10f;
    echo       var atk = m.GetAttackModel^(^);
    echo       atk.weapons[0].Rate = 1.1f;
    echo       var p = atk.weapons[0].projectile;
    echo       p.pierce += 2;
    echo       p.GetDamageModel^(^).damage = 2;
    echo     }
    echo   }
    echo }
    ^) ^> "%TOWER_FILE%"
    
    echo Building simplified version...
    dotnet build -c Release --nologo
    
    if %ERRORLEVEL% EQU 0 ^(
        echo SUCCESS! Lightning Monkey enhanced ^(simplified version^)!
        echo - Now based on Wizard Monkey ^(better appearance^)
        echo - Enhanced stats and lightning-like behavior
    ^) else ^(
        echo Build still failed. Check errors above.
    ^)
)
pause
