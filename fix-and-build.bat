@echo off
echo Fixing Lightning Monkey project and building...

set "PROJ=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\LightningMonkey.csproj"

if not exist "%PROJ%" (
    echo Project not found: %PROJ%
    pause
    exit /b 1
)

echo Updating target framework to net6.0...
powershell -NoProfile -Command "(Get-Content '%PROJ%' -Raw) -replace '<TargetFramework>net48</TargetFramework>','<TargetFramework>net6.0</TargetFramework>' | Set-Content '%PROJ%'"

echo Building Release...
cd /d "%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
dotnet build -c Release --nologo

echo.
echo Build complete! Check above for any errors.
echo If successful, LightningMonkey.dll was copied to your Mods folder.
pause
