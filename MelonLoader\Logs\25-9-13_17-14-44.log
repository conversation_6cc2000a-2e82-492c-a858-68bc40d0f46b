
[17:14:44.621] ------------------------------
[17:14:44.624] MelonLoader v0.7.1 Open-Beta
[17:14:44.625] OS: Windows 11
[17:14:44.625] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[17:14:44.625] ------------------------------
[17:14:44.625] Game Type: Il2cpp
[17:14:44.626] Game Arch: x64
[17:14:44.626] ------------------------------
[17:14:44.626] Command-Line: 
[17:14:44.626] ------------------------------
[17:14:44.626] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:14:44.626] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:14:44.626] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[17:14:44.627] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[17:14:44.627] Runtime Type: net6
[17:14:44.688] ------------------------------
[17:14:44.688] Game Name: BloonsTD6
[17:14:44.689] Game Developer: Ninja Kiwi
[17:14:44.690] Unity Version: 6000.0.52f1
[17:14:44.690] Game Version: 50.2
[17:14:44.690] ------------------------------

[17:14:45.075] Preferences Loaded!

[17:14:45.088] Loading UserLibs...
[17:14:45.090] 0 UserLibs loaded.

[17:14:45.090] Loading Plugins...
[17:14:45.094] 0 Plugins loaded.

[17:14:45.607] Loading Il2CppAssemblyGenerator...
[17:14:45.650] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[17:14:45.966] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[17:14:45.966] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[17:14:45.967] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[17:14:45.967] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[17:14:45.974] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[17:14:45.974] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[17:14:45.974] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[17:14:45.974] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[17:14:45.974] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[17:14:45.975] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[17:14:45.975] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[17:14:45.975] [Il2CppAssemblyGenerator] Checking GameAssembly...
[17:14:46.143] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[17:14:46.144] Loading Mods...
[17:14:46.191] ------------------------------
[17:14:46.229] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[17:14:46.229] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[17:14:46.232] Melon Assembly loaded: '.\Mods\EditPlayerData.dll'
[17:14:46.232] SHA256 Hash: 'D0AE1A904057FF46AF12EA6A6856E28954B9DE4191999802288DA1DD00C4483D'
[17:14:46.233] Melon Assembly loaded: '.\Mods\FasterForward.dll'
[17:14:46.233] SHA256 Hash: '813ABC55334F4CBE44340FFEA724F693D03BDBB189CF468D58E9CA30CB208F9D'
[17:14:46.235] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[17:14:46.235] SHA256 Hash: 'C23277E37C12E91D3C47EB6A2548626CCE3111679FC4770DDEA2C06B5B77C9E9'
[17:14:46.239] Melon Assembly loaded: '.\Mods\PowersInShop.dll'
[17:14:46.239] SHA256 Hash: '3FACA23924F13CA0460D52917FD217ABD03463E123B748C8B67CAECC38EC9BAA'
[17:14:46.321] Melon Assembly loaded: '.\Mods\UltimateCrosspathing.dll'
[17:14:46.321] SHA256 Hash: 'E6D1273C0FA64DDB5673925855384E1C1B757AAA6938276274354285ACD889DE'
[17:14:46.334] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[17:14:46.334] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[17:14:46.924] ------------------------------
[17:14:46.924] BloonsTD6 Mod Helper v3.4.12
[17:14:46.925] by Gurrenm4 and Doombubbles
[17:14:46.925] Assembly: Btd6ModHelper.dll
[17:14:46.925] ------------------------------
[17:14:46.927] ------------------------------
[17:14:46.927] Ultimate Crosspathing v1.7.1
[17:14:46.927] by doombubbles
[17:14:46.927] Assembly: UltimateCrosspathing.dll
[17:14:46.928] ------------------------------
[17:14:46.930] ------------------------------
[17:14:46.930] EditPlayerData v1.5.1
[17:14:46.930] by MaliciousFiles
[17:14:46.930] Assembly: EditPlayerData.dll
[17:14:46.930] ------------------------------
[17:14:46.933] ------------------------------
[17:14:46.933] Faster Forward v1.1.5
[17:14:46.933] by doombubbles
[17:14:46.933] Assembly: FasterForward.dll
[17:14:46.933] ------------------------------
[17:14:46.935] ------------------------------
[17:14:46.935] Lightning Monkey Mod v1.0.0
[17:14:46.935] by You
[17:14:46.935] Assembly: LightningMonkey.dll
[17:14:46.935] ------------------------------
[17:14:46.937] ------------------------------
[17:14:46.937] Powers in Shop v3.0.3
[17:14:46.937] by doombubbles
[17:14:46.938] Assembly: PowersInShop.dll
[17:14:46.938] ------------------------------
[17:14:46.940] ------------------------------
[17:14:46.940] Unlimited 5th Tiers + v1.1.9
[17:14:46.940] by doombubbles
[17:14:46.940] Assembly: Unlimited5thTiers.dll
[17:14:46.940] ------------------------------
[17:14:46.940] ------------------------------
[17:14:46.940] 7 Mods loaded.

[17:14:47.987] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[17:14:48.158] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[17:14:48.185] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[17:14:48.189] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[17:14:48.202] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[17:14:48.204] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[17:14:48.288] [Il2CppInterop] Method Void SetNumColumns(Int32, Int32[]) on type EditPlayerData.UI.ModHelperTable has unsupported parameter Int32[] colFlex of type System.Int32[]
[17:14:48.290] [Il2CppInterop] Method Void SetSetting(EditPlayerData.UI.PlayerDataSetting) on type EditPlayerData.UI.PlayerDataSettingDisplay has unsupported parameter EditPlayerData.UI.PlayerDataSetting setting of type EditPlayerData.UI.PlayerDataSetting
[17:14:48.638] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[17:14:49.109] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[17:14:49.359] [EditPlayerData] Melon Assembly loaded: 'C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Windows.Forms.dll'
[17:14:49.359] [EditPlayerData] SHA256 Hash: 'F8EE5BFDC1FE7508773CEB997F6C78FC8CF8CDBAE11F0DEB15E78AA94BEBFED7'
[17:14:49.361] [EditPlayerData] EditPlayerData loaded!
[17:14:49.501] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[17:14:50.030] [BloonsTD6_Mod_Helper] AlchemistLoader finished loading bytes
[17:14:50.095] [BloonsTD6_Mod_Helper] BananaFarmLoader finished loading bytes
[17:14:50.137] [BloonsTD6_Mod_Helper] BananaFarmerProLoader finished loading bytes
[17:14:50.331] [BloonsTD6_Mod_Helper] BeastHandlerLoader finished loading bytes
[17:14:50.421] [BloonsTD6_Mod_Helper] BombShooterLoader finished loading bytes
[17:14:50.488] [BloonsTD6_Mod_Helper] BoomerangMonkeyLoader finished loading bytes
[17:14:50.563] [BloonsTD6_Mod_Helper] DartMonkeyLoader finished loading bytes
[17:14:50.723] [BloonsTD6_Mod_Helper] DartlingGunnerLoader finished loading bytes
[17:14:50.859] [BloonsTD6_Mod_Helper] DesperadoLoader finished loading bytes
[17:14:50.993] [BloonsTD6_Mod_Helper] DruidLoader finished loading bytes
[17:14:51.167] [BloonsTD6_Mod_Helper] EngineerMonkeyLoader finished loading bytes
[17:14:51.241] [BloonsTD6_Mod_Helper] GlueGunnerLoader finished loading bytes
[17:14:51.411] [BloonsTD6_Mod_Helper] HeliPilotLoader finished loading bytes
[17:14:51.529] [BloonsTD6_Mod_Helper] IceMonkeyLoader finished loading bytes
[17:14:51.773] [BloonsTD6_Mod_Helper] MermonkeyLoader finished loading bytes
[17:14:51.897] [BloonsTD6_Mod_Helper] MonkeyAceLoader finished loading bytes
[17:14:52.141] [BloonsTD6_Mod_Helper] MonkeyBuccaneerLoader finished loading bytes
[17:14:52.237] [BloonsTD6_Mod_Helper] MonkeySubLoader finished loading bytes
[17:14:52.315] [BloonsTD6_Mod_Helper] MonkeyVillageLoader finished loading bytes
[17:14:52.411] [BloonsTD6_Mod_Helper] MortarMonkeyLoader finished loading bytes
[17:14:52.468] [BloonsTD6_Mod_Helper] NinjaMonkeyLoader finished loading bytes
[17:14:52.542] [BloonsTD6_Mod_Helper] SniperMonkeyLoader finished loading bytes
[17:14:52.707] [BloonsTD6_Mod_Helper] SpikeFactoryLoader finished loading bytes
[17:14:52.735] [BloonsTD6_Mod_Helper] SuperMonkeyBeaconLoader finished loading bytes
[17:14:53.037] [BloonsTD6_Mod_Helper] SuperMonkeyLoader finished loading bytes
[17:14:53.083] [BloonsTD6_Mod_Helper] TackShooterLoader finished loading bytes
[17:14:53.335] [BloonsTD6_Mod_Helper] WizardMonkeyLoader finished loading bytes
[17:14:55.959] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 378 mods over 6.8 seconds
[17:14:59.343] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[17:14:59.346] [BloonsTD6_Mod_Helper] Registering ModContent for Ultimate Crosspathing...
[17:14:59.402] [Ultimate_Crosspathing] Finished loading DartMonkeys!
[17:14:59.425] [Ultimate_Crosspathing] Finished loading BoomerangMonkeys!
[17:14:59.558] [Ultimate_Crosspathing] Finished loading BombShooters!
[17:14:59.616] [Ultimate_Crosspathing] Finished loading TackShooters!
[17:14:59.661] [Ultimate_Crosspathing] Finished loading IceMonkeys!
[17:14:59.711] [Ultimate_Crosspathing] Finished loading GlueGunners!
[17:14:59.740] [Ultimate_Crosspathing] Finished loading Desperados!
[17:14:59.778] [Ultimate_Crosspathing] Finished loading SniperMonkeys!
[17:14:59.838] [Ultimate_Crosspathing] Finished loading MonkeySubs!
[17:14:59.901] [Ultimate_Crosspathing] Finished loading MonkeyBuccaneers!
[17:14:59.931] [Ultimate_Crosspathing] Finished loading MonkeyAces!
[17:14:59.978] [Ultimate_Crosspathing] Finished loading HeliPilots!
[17:15:00.041] [Ultimate_Crosspathing] Finished loading MortarMonkeys!
[17:15:00.094] [Ultimate_Crosspathing] Finished loading DartlingGunners!
[17:15:00.142] [Ultimate_Crosspathing] Finished loading WizardMonkeys!
[17:15:00.220] [Ultimate_Crosspathing] Finished loading SuperMonkeys!
[17:15:00.258] [Ultimate_Crosspathing] Finished loading NinjaMonkeys!
[17:15:00.315] [Ultimate_Crosspathing] Finished loading Alchemists!
[17:15:00.361] [Ultimate_Crosspathing] Finished loading Druids!
[17:15:00.428] [Ultimate_Crosspathing] Finished loading Mermonkeys!
[17:15:00.477] [Ultimate_Crosspathing] Finished loading BananaFarms!
[17:15:00.541] [Ultimate_Crosspathing] Finished loading SpikeFactorys!
[17:15:00.584] [Ultimate_Crosspathing] Finished loading MonkeyVillages!
[17:15:00.630] [Ultimate_Crosspathing] Finished loading EngineerMonkeys!
[17:15:00.668] [Ultimate_Crosspathing] Finished loading BeastHandlers!
[17:15:00.681] [Ultimate_Crosspathing] Finished loading BananaFarmerPros!
[17:15:00.694] [Ultimate_Crosspathing] Finished loading SuperMonkeyBeacons!
[17:15:00.694] [BloonsTD6_Mod_Helper] Registering ModContent for EditPlayerData...
[17:15:00.694] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[17:15:00.873] [BloonsTD6_Mod_Helper] Registering ModContent for Powers in Shop...
[17:15:00.917] [BloonsTD6_Mod_Helper] Registering ModContent for Unlimited 5th Tiers +...
[17:15:29.793] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:29.795] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:29.797] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:29.797] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:33.657] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:33.660] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:33.661] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:33.661] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:45.760] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:45.763] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:45.764] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:15:45.765] [Il2CppInterop] During invoking native->managed trampoline
System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Patches.UI.TowerImageLoader_Load.Postfix(TowerImageLoader __instance, String towerID, Boolean useRoundBg)
   at DMD<Il2Cpp.TowerImageLoader::Load>(TowerImageLoader this, String towerID, Boolean instaTower, Boolean dontShowLockedState, Boolean showTowerSetBackground, Boolean useRoundBg, Il2CppStructArray`1 instaTowerTiers, Boolean useLevel1Portrait)
   at (il2cpp -> managed) Load(IntPtr , IntPtr , Byte , Byte , Byte , Byte , IntPtr , Byte , Il2CppMethodInfo* )
[17:24:21.415] [Il2CppInterop] During invoking native->managed trampoline
Il2CppInterop.Runtime.Il2CppException: System.NullReferenceException: Object reference not set to an instance of an object.
--- BEGIN IL2CPP STACK TRACE ---
System.NullReferenceException: Object reference not set to an instance of an object.
  at Assets.Scripts.Simulation.Towers.Behaviors.HeliMovement.OnChangeTargetPriority (Assets.Scripts.Models.Towers.TargetType targetType) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.SetTargetType (Assets.Scripts.Models.Towers.TargetType type) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.Initialise (Assets.Scripts.Simulation.Objects.Entity target, Assets.Scripts.Models.Model modelToUse) [0x00000] in <00000000000000000000000000000000>:0 
--- END IL2CPP STACK TRACE ---

   at Il2CppInterop.Runtime.Il2CppException.RaiseExceptionIfNecessary(IntPtr returnedException) in /home/<USER>/work/Il2CppInterop/Il2CppInterop/Il2CppInterop.Runtime/Il2CppException.cs:line 36
   at DMD<Il2CppAssets.Scripts.Simulation.Towers.Tower::Initialise>(Tower this, Entity target, Model modelToUse)
   at (il2cpp -> managed) Initialise(IntPtr , IntPtr , IntPtr , Il2CppMethodInfo* )
[17:24:38.492] [Il2CppInterop] During invoking native->managed trampoline
Il2CppInterop.Runtime.Il2CppException: System.NullReferenceException: Object reference not set to an instance of an object.
--- BEGIN IL2CPP STACK TRACE ---
System.NullReferenceException: Object reference not set to an instance of an object.
  at Assets.Scripts.Simulation.Towers.Behaviors.HeliMovement.OnChangeTargetPriority (Assets.Scripts.Models.Towers.TargetType targetType) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.SetTargetType (Assets.Scripts.Models.Towers.TargetType type) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.Initialise (Assets.Scripts.Simulation.Objects.Entity target, Assets.Scripts.Models.Model modelToUse) [0x00000] in <00000000000000000000000000000000>:0 
--- END IL2CPP STACK TRACE ---

   at Il2CppInterop.Runtime.Il2CppException.RaiseExceptionIfNecessary(IntPtr returnedException) in /home/<USER>/work/Il2CppInterop/Il2CppInterop/Il2CppInterop.Runtime/Il2CppException.cs:line 36
   at DMD<Il2CppAssets.Scripts.Simulation.Towers.Tower::Initialise>(Tower this, Entity target, Model modelToUse)
   at (il2cpp -> managed) Initialise(IntPtr , IntPtr , IntPtr , Il2CppMethodInfo* )
[17:31:55.504] [Il2CppInterop] During invoking native->managed trampoline
Il2CppInterop.Runtime.Il2CppException: System.NullReferenceException: Object reference not set to an instance of an object.
--- BEGIN IL2CPP STACK TRACE ---
System.NullReferenceException: Object reference not set to an instance of an object.
  at Assets.Scripts.Simulation.Towers.Behaviors.HeliMovement.OnChangeTargetPriority (Assets.Scripts.Models.Towers.TargetType targetType) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.SetTargetType (Assets.Scripts.Models.Towers.TargetType type) [0x00000] in <00000000000000000000000000000000>:0 
  at Assets.Scripts.Simulation.Towers.Tower.Initialise (Assets.Scripts.Simulation.Objects.Entity target, Assets.Scripts.Models.Model modelToUse) [0x00000] in <00000000000000000000000000000000>:0 
--- END IL2CPP STACK TRACE ---

   at Il2CppInterop.Runtime.Il2CppException.RaiseExceptionIfNecessary(IntPtr returnedException) in /home/<USER>/work/Il2CppInterop/Il2CppInterop/Il2CppInterop.Runtime/Il2CppException.cs:line 36
   at DMD<Il2CppAssets.Scripts.Simulation.Towers.Tower::Initialise>(Tower this, Entity target, Model modelToUse)
   at (il2cpp -> managed) Initialise(IntPtr , IntPtr , IntPtr , Il2CppMethodInfo* )
[17:43:50.099] Preferences Saved!
