@echo off
echo Creating Dark Lightning Monkey with black hole magic...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"
set "BLACKHOLE_UPGRADE=%PROJ_DIR%\Towers\Upgrades\Top_T5_VoidStorm.cs"
set "CHAIN_UPGRADE=%PROJ_DIR%\Towers\Upgrades\Mid_T3_ChainLightning.cs"

echo Writing Dark Lightning Monkey with original appearance...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Weapons;
echo using Il2CppAssets.Scripts.Unity;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     // Use Dart Monkey base for normal brown monkey appearance
echo     public override string BaseTower =^> TowerType.DartMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Dark Lightning Monkey";
echo     public override string Description =^> "A mystical monkey wielding cyan lightning staff! Shoots crackling lightning bolts and can warp reality with dark magic black holes.";
echo     public override int Cost =^> 750;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel towerModel^) {
echo       // Enhanced range for magical staff
echo       towerModel.range += 18f;
echo.
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var weaponModel = attackModel.weapons[0];
echo         
echo         // Lightning staff attack rate
echo         weaponModel.Rate = 0.8f;
echo.
echo         var projectile = weaponModel.projectile;
echo         // Cyan lightning bolt stats
echo         projectile.pierce = 5f;
echo         projectile.GetDamageModel^(^).damage = 4f;
echo         projectile.scale = 1.4f;
echo       }
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Writing Chain Lightning upgrade...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class Mid_T3_ChainLightning : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 3;
echo     public override int Cost =^> 1400;
echo.
echo     public override string DisplayName =^> "Chain Lightning";
echo     public override string Description =^> "Lightning bolts chain between nearby bloons with crackling electrical arcs!";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo.
echo         // Create chain projectile
echo         var chainProjectile = projectile.Duplicate^(^);
echo         chainProjectile.pierce = 3f;
echo         chainProjectile.GetDamageModel^(^).damage = 3f;
echo         chainProjectile.scale = 0.9f;
echo.
echo         // Add chain behavior
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo           "ChainLightning",
echo           chainProjectile,
echo           new SingleEmissionModel^("", null^),
echo           true, false, false
echo         ^)^);
echo.
echo         projectile.pierce += 3f;
echo         projectile.GetDamageModel^(^).damage += 2f;
echo       }
echo     }
echo   }
echo }
) > "%CHAIN_UPGRADE%"

echo Writing Black Hole Void Storm upgrade...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Attack;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class Top_T5_VoidStorm : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 5;
echo     public override int Cost =^> 45000;
echo.
echo     public override string DisplayName =^> "Void Storm Master";
echo     public override string Description =^> "DARK MAGIC: Warps reality to create devastating black holes that consume bloons into the void! Lightning becomes infused with dark energy.";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo.
echo         // Dark lightning stats
echo         projectile.pierce = 15f;
echo         projectile.GetDamageModel^(^).damage = 25f;
echo         projectile.scale = 2.0f;
echo.
echo         // Create black hole effect on impact
echo         var blackHoleProjectile = projectile.Duplicate^(^);
echo         blackHoleProjectile.pierce = 999f;
echo         blackHoleProjectile.GetDamageModel^(^).damage = 50f;
echo         blackHoleProjectile.scale = 3.0f;
echo.
echo         // Add black hole creation behavior
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo           "BlackHoleVoid",
echo           blackHoleProjectile,
echo           new SingleEmissionModel^("", null^),
echo           true, false, false
echo         ^)^);
echo.
echo         // Faster attack rate for void storm
echo         attackModel.weapons[0].Rate = 0.3f;
echo       }
echo     }
echo   }
echo }
) > "%BLACKHOLE_UPGRADE%"

echo Building Dark Lightning Monkey...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Dark Lightning Monkey is ready!
    echo ========================================
    echo.
    echo PERFECT MATCH for your image:
    echo - Normal brown monkey appearance ^(like your image^)
    echo - Cyan lightning staff weapon
    echo - Enhanced range +18 ^(magical staff^)
    echo - 4 damage cyan lightning bolts
    echo - 5 pierce base
    echo - 1.4x larger projectiles
    echo - 0.8s attack rate
    echo.
    echo CHAIN LIGHTNING ^(Middle T3 - 1400 cost^):
    echo - Lightning chains between nearby bloons!
    echo - Creates electrical arcs jumping targets
    echo - +3 pierce, +2 damage to main bolt
    echo - Chain bolts: 3 damage, 3 pierce
    echo.
    echo VOID STORM MASTER ^(Top T5 - 45000 cost^):
    echo - DARK MAGIC: Creates black holes!
    echo - Warps bloons into the void
    echo - 25 damage dark lightning
    echo - 15 pierce main bolt
    echo - Black hole: 50 damage, 999 pierce
    echo - 0.3s attack rate ^(super fast^)
    echo - 2x larger dark projectiles
    echo.
    echo Launch BTD6 to see your Dark Lightning Monkey!
    echo ^(Magic category, 750 cost^)
) else (
    echo Build failed. Check errors above.
)
pause
