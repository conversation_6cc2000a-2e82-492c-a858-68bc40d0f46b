$ErrorActionPreference = 'Stop'
$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$tdir = Join-Path $root 'Towers'
$updir = Join-Path $tdir 'Upgrades'
$rf = Join-Path $root 'Resources'
New-Item -ItemType Directory -Force -Path $tdir | Out-Null
New-Item -ItemType Directory -Force -Path $updir | Out-Null
New-Item -ItemType Directory -Force -Path $rf   | Out-Null

# Cyclops base tower
$cyclops = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.TowerSets;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers {
  public class Cyclops : ModTower {
    public override string BaseTower => TowerType.SuperMonkey;
    public override TowerSet TowerSet => TowerSet.Magic;

    public override string DisplayName => "Cyclops";
    public override string Description => "Leader of the X-Men unleashing precise ruby optic blasts; commands the battlefield with tactical prowess.";
    public override int Cost => 18000; // mid-high tier
    public override string Icon => "Cyclops-Icon";
    public override string Portrait => "Cyclops-Portrait";

    public override int TopPathUpgrades => 5;
    public override int MiddlePathUpgrades => 5;
    public override int BottomPathUpgrades => 5;

    public override void ModifyBaseTowerModel(TowerModel t) {
      // Strong baseline beam; red optics vibe
      t.range += 30f;
      var attack = t.GetAttackModel();
      if (attack != null) {
        // use SuperMonkey-200 beam as base and amp it
        var sm200 = Game.instance.model.GetTowerFromId("SuperMonkey-200");
        if (sm200 != null) {
          var beam = sm200.GetAttackModel().Duplicate();
          foreach (var w in beam.weapons) {
            w.Rate *= 0.6f; // faster
            var p = w.projectile;
            var d = p.GetDamageModel(); if (d != null) d.damage += 4f;
            p.pierce += 10f;
            p.scale *= 1.2f; // thicker beam
          }
          t.RemoveBehavior(attack);
          t.AddBehavior(beam);
        }
      }

      // Inherent camo detection: disable camo filters on all attacks
      try {
        foreach (var a in t.GetAttackModels())
          foreach (var w in a.weapons) {
            var p = w.projectile; var f = p.GetBehavior<FilterInvisibleModel>(); if (f != null) f.isActive = false;
          }
      } catch {}
    }
  }
}
'@
[IO.File]::WriteAllText((Join-Path $tdir 'Cyclops.cs'), $cyclops, [Text.Encoding]::UTF8)

# Top Path upgrades
$top = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  // Optic Mastery
  public class EnhancedOptics : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => TOP; public override int Tier => 1; public override int Cost => 900;
    public override string DisplayName => "Enhanced Optics";
    public override string Description => "Increased range and pierce.";
    public override void ApplyUpgrade(TowerModel t) {
      t.range += 15f; var p = t.GetAttackModel().weapons[0].projectile; p.pierce += 15f;
    }
  }
  public class FocusedBeam : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => TOP; public override int Tier => 2; public override int Cost => 1800;
    public override string DisplayName => "Focused Beam";
    public override string Description => "Higher damage, faster attack speed.";
    public override void ApplyUpgrade(TowerModel t) {
      var w = t.GetAttackModel().weapons[0]; w.Rate *= 0.75f; var d = w.projectile.GetDamageModel(); if (d != null) d.damage += 6f;
    }
  }
  public class ConcussiveBlast : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => TOP; public override int Tier => 3; public override int Cost => 3600;
    public override string DisplayName => "Concussive Blast";
    public override string Description => "Adds knockback force to optic blasts.";
    public override void ApplyUpgrade(TowerModel t) {
      // Borrow pushback effect by using Druid-005 tornado projectile as secondary tiny force
      var druid005 = Game.instance.model.GetTowerFromId("Druid-005");
      if (druid005 != null) {
        var pushProj = druid005.GetAttackModel().weapons[0].projectile.Duplicate();
        pushProj.pierce = 1f; var dmg = pushProj.GetDamageModel(); if (dmg!=null) dmg.damage = 0;
        var w2 = t.GetAttackModel().weapons[0].Duplicate(); w2.name = "ConcussivePulse"; w2.projectile = pushProj; w2.Rate = 0.9f;
        t.GetAttackModel().AddWeapon(w2);
      }
    }
  }
  public class RubyQuartzVisor : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => TOP; public override int Tier => 4; public override int Cost => 12000;
    public override string DisplayName => "Ruby Quartz Visor";
    public override string Description => "Massive damage boost and beam width.";
    public override void ApplyUpgrade(TowerModel t) {
      var w = t.GetAttackModel().weapons[0]; var p = w.projectile; var d = p.GetDamageModel(); if (d!=null) d.damage += 25f; p.scale *= 1.6f;
    }
  }
  public class OmegaLevelMutant : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => TOP; public override int Tier => 5; public override int Cost => 85000;
    public override string DisplayName => "Omega-Level Mutant";
    public override string Description => "Screen-clearing mega beam with extreme damage and pierce.";
    public override void ApplyUpgrade(TowerModel t) {
      // Add a Ray-of-Doom style beam as an extra weapon
      var dg520 = Game.instance.model.GetTowerFromId("DartlingGunner-520");
      if (dg520 != null) {
        var mega = dg520.GetAttackModel().weapons[0].Duplicate();
        mega.name = "OmegaBeam"; mega.Rate = 0.4f;
        var p = mega.projectile; var d = p.GetDamageModel(); if (d!=null) d.damage *= 3f; p.pierce = 9999f; p.scale *= 1.4f;
        t.GetAttackModel().AddWeapon(mega);
      }
    }
  }
}
'@
[IO.File]::WriteAllText((Join-Path $updir 'CyclopsTop.cs'), $top, [Text.Encoding]::UTF8)

# Middle Path upgrades (Leadership) – approximate with strong auras/dup behaviors
$mid = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  public class FieldCommander : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => MIDDLE; public override int Tier => 1; public override int Cost => 1200;
    public override string DisplayName => "Field Commander";
    public override string Description => "Buffs nearby towers' attack speed.";
    public override void ApplyUpgrade(TowerModel t) {
      // Borrow Village-200 Jungle Drums support
      var v200 = Game.instance.model.GetTowerFromId("MonkeyVillage-200");
      if (v200 != null) foreach (var b in v200.behaviors) t.AddBehavior(b.Duplicate());
    }
  }
  public class StrategicVision : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => MIDDLE; public override int Tier => 2; public override int Cost => 2400;
    public override string DisplayName => "Strategic Vision";
    public override string Description => "Increases range of nearby towers.";
    public override void ApplyUpgrade(TowerModel t) {
      var v002 = Game.instance.model.GetTowerFromId("MonkeyVillage-002");
      if (v002 != null) foreach (var b in v002.behaviors) t.AddBehavior(b.Duplicate());
    }
  }
  public class XMenCoordination : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => MIDDLE; public override int Tier => 3; public override int Cost => 5200;
    public override string DisplayName => "X-Men Coordination";
    public override string Description => "Adds damage buffs to surrounding towers.";
    public override void ApplyUpgrade(TowerModel t) {
      var v230 = Game.instance.model.GetTowerFromId("MonkeyVillage-230");
      if (v230 != null) foreach (var b in v230.behaviors) t.AddBehavior(b.Duplicate());
    }
  }
  public class BattlefieldTactics : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => MIDDLE; public override int Tier => 4; public override int Cost => 14000;
    public override string DisplayName => "Battlefield Tactics";
    public override string Description => "Global buffs and reveals all camo bloons map-wide.";
    public override void ApplyUpgrade(TowerModel t) {
      var v250 = Game.instance.model.GetTowerFromId("MonkeyVillage-250");
      if (v250 != null) foreach (var b in v250.behaviors) t.AddBehavior(b.Duplicate());
    }
  }
  public class XMenLeader : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => MIDDLE; public override int Tier => 5; public override int Cost => 45000;
    public override string DisplayName => "X-Men Leader";
    public override string Description => "Massive global buffs and calls in allies periodically.";
    public override void ApplyUpgrade(TowerModel t) {
      // Add stronger village behaviors again and a periodic global beam attack
      var v550 = Game.instance.model.GetTowerFromId("MonkeyVillage-520");
      if (v550 != null) foreach (var b in v550.behaviors) t.AddBehavior(b.Duplicate());
      var dg520 = Game.instance.model.GetTowerFromId("DartlingGunner-520");
      if (dg520 != null) {
        var atk = dg520.GetAttackModel().Duplicate(); atk.weapons[0].Rate = 2.5f; // periodic mega beam as "ally"
        t.AddBehavior(atk);
      }
    }
  }
}
'@
[IO.File]::WriteAllText((Join-Path $updir 'CyclopsMiddle.cs'), $mid, [Text.Encoding]::UTF8)

# Bottom Path upgrades – Optic Versatility
$bot = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  public class WideBeam : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => BOTTOM; public override int Tier => 1; public override int Cost => 900;
    public override string DisplayName => "Wide Beam";
    public override string Description => "Attacks multiple targets simultaneously.";
    public override void ApplyUpgrade(TowerModel t) {
      var atk = t.GetAttackModel(); var w2 = atk.weapons[0].Duplicate(); w2.name = "WideBeam2"; w2.Rate *= 1.0f; atk.AddWeapon(w2);
    }
  }
  public class RicochetBlast : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => BOTTOM; public override int Tier => 2; public override int Cost => 2000;
    public override string DisplayName => "Ricochet Blast";
    public override string Description => "Beams bounce between enemies.";
    public override void ApplyUpgrade(TowerModel t) {
      // Add a bouncing attack borrowed from Boomerang 020
      var boomer020 = Game.instance.model.GetTowerFromId("BoomerangMonkey-020");
      if (boomer020 != null) t.AddBehavior(boomer020.GetAttackModel().Duplicate());
    }
  }
  public class HeatVision : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => BOTTOM; public override int Tier => 3; public override int Cost => 3500;
    public override string DisplayName => "Heat Vision";
    public override string Description => "Adds burn damage over time effects.";
    public override void ApplyUpgrade(TowerModel t) {
      // Add small burning aura from Tack 400 to emulate burn over time
      var tack400 = Game.instance.model.GetTowerFromId("TackShooter-400");
      if (tack400 != null) { var aura = tack400.GetAttackModel().Duplicate(); aura.name = "HeatAura"; aura.range = t.range; aura.weapons[0].Rate = 0.5f; t.AddBehavior(aura); }
    }
  }
  public class OpticArray : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => BOTTOM; public override int Tier => 4; public override int Cost => 14000;
    public override string DisplayName => "Optic Array";
    public override string Description => "Fires multiple beams in different directions.";
    public override void ApplyUpgrade(TowerModel t) {
      var atk = t.GetAttackModel();
      for (int i=0;i<3;i++) { var w = atk.weapons[0].Duplicate(); w.name = "ArrayBeam"+i; w.Rate *= 0.9f; atk.AddWeapon(w); }
    }
  }
  public class PhoenixForceCyclops : ModUpgrade<LightningMonkey.Towers.Cyclops> {
    public override int Path => BOTTOM; public override int Tier => 5; public override int Cost => 90000;
    public override string DisplayName => "Phoenix Force Cyclops";
    public override string Description => "Transforms into Phoenix-powered form with devastating area attacks.";
    public override void ApplyUpgrade(TowerModel t) {
      // Add massive aura and a periodic mega beam
      var inferno = Game.instance.model.GetTowerFromId("TackShooter-520"); if (inferno != null) { var aura = inferno.GetAttackModel().Duplicate(); aura.name = "PhoenixAura"; aura.range = t.range + 50f; aura.weapons[0].Rate = 0.2f; var p=aura.weapons[0].projectile; var d=p.GetDamageModel(); if(d!=null)d.damage*=2f; p.pierce=9999f; p.scale*=2.5f; t.AddBehavior(aura);} 
      var dg520 = Game.instance.model.GetTowerFromId("DartlingGunner-520"); if (dg520 != null) { var beamAtk = dg520.GetAttackModel().Duplicate(); beamAtk.weapons[0].Rate = 1.5f; t.AddBehavior(beamAtk); }
      t.displayScale *= 1.2f;
    }
  }
}
'@
[IO.File]::WriteAllText((Join-Path $updir 'CyclopsBottom.cs'), $bot, [Text.Encoding]::UTF8)

# Add placeholder assets if available
$lmIcon = Join-Path $rf 'LightningMonkey-Icon.png'
$lmPor  = Join-Path $rf 'LightningMonkey-Portrait.png'
$cxIcon = Join-Path $rf 'Cyclops-Icon.png'
$cxPor  = Join-Path $rf 'Cyclops-Portrait.png'
if (Test-Path $lmIcon -PathType Leaf) { Copy-Item $lmIcon $cxIcon -Force }
if (Test-Path $lmPor  -PathType Leaf) { Copy-Item $lmPor  $cxPor  -Force }

Write-Host 'Cyclops tower and upgrades added (with placeholder assets if present).'

