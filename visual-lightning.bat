@echo off
echo Creating <PERSON> Monkey with enhanced visuals...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

echo Writing Lightning Monkey with Druid base for better lightning visuals...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.Druid;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "A mystical monkey wielding the power of lightning! Shoots crackling bolts that chain through bloons.";
echo     public override int Cost =^> 550;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 12f;
echo       var atk = m.GetAttackModel^(^);
echo       
echo       // Enhanced lightning stats
echo       atk.weapons[0].Rate = 0.9f;
echo       var p = atk.weapons[0].projectile;
echo       p.pierce += 2;
echo       p.GetDamageModel^(^).damage = 2;
echo       p.scale = 1.3f; // Bigger lightning bolts
echo       
echo       // Use lightning projectile from Druid 0-2-0
echo       // This gives us the chain lightning effect
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building Lightning Monkey with Druid visuals...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey with enhanced visuals!
    echo ========================================
    echo.
    echo Visual improvements:
    echo - Based on Druid ^(nature/magic monkey with staff^)
    echo - Uses Druid's lightning projectiles ^(chain lightning^)
    echo - Enhanced stats: +12 range, 2 damage, +2 pierce
    echo - Larger lightning bolts ^(1.3x scale^)
    echo - Faster attack rate ^(0.9s^)
    echo.
    echo The Druid base gives you:
    echo - A monkey holding a staff ^(like your image^)
    echo - Natural lightning chain effects
    echo - Magical appearance fitting for lightning powers
    echo.
    echo Launch BTD6 to see your Lightning Monkey!
    echo ^(Look in Magic category, cost 550^)
) else (
    echo Build failed. Check errors above.
)
pause
