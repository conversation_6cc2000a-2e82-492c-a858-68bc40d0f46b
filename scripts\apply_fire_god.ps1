$ErrorActionPreference = 'Stop'

$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$lt = Join-Path $root 'Towers\LightningMonkey.cs'
$csproj = Join-Path $root 'LightningMonkey.csproj'
$upg = Join-Path $root 'Towers\Upgrades\AllUpgrades.cs'
$resDir = Join-Path $root 'Resources'

if (-not (Test-Path -LiteralPath $lt)) { throw "Missing file: $lt" }
if (-not (Test-Path -LiteralPath $csproj)) { throw "Missing file: $csproj" }
if (-not (Test-Path -LiteralPath $upg)) { throw "Missing file: $upg" }
if (-not (Test-Path -LiteralPath $resDir)) { New-Item -ItemType Directory -Path $resDir | Out-Null }

# 1x1 placeholder PNGs (replace with your art later)
$pngBase64Blue = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGP4////fwAJ+wP9AAAAAElFTkSuQmCC'
$pngBase64Orange = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGP4//8/AwMDgAEAAZ6fBVoAAAAASUVORK5CYII='
[IO.File]::WriteAllBytes((Join-Path $resDir 'LightningMonkey-Icon.png'), [Convert]::FromBase64String($pngBase64Blue))
[IO.File]::WriteAllBytes((Join-Path $resDir 'LightningMonkey-Portrait.png'), [Convert]::FromBase64String($pngBase64Orange))

# Insert Icon/Portrait overrides into ModTower if missing
$src = Get-Content -Raw -LiteralPath $lt
if ($src -notmatch 'public\s+override\s+string\s+Icon') {
  $src = $src -replace 'public\s+override\s+int\s+Cost\s*=>\s*\d+;', "public override int Cost => 650;`n`n        public override string Icon => \"LightningMonkey-Icon\";`n        public override string Portrait => \"LightningMonkey-Portrait\";"
  Set-Content -LiteralPath $lt -Encoding UTF8 -Value $src
}

# Ensure Resources are embedded in csproj
[xml]$doc = Get-Content -Raw -LiteralPath $csproj
$hasEmbed = $false
foreach ($ig in $doc.Project.ItemGroup) {
  foreach ($n in $ig.ChildNodes) { if ($n.Name -eq 'EmbeddedResource' -and $n.Include -like 'Resources*') { $hasEmbed = $true } }
}
if (-not $hasEmbed) {
  $ig = $doc.CreateElement('ItemGroup')
  $er = $doc.CreateElement('EmbeddedResource')
  $er.SetAttribute('Include','Resources\\**\\*.png')
  [void]$ig.AppendChild($er)
  [void]$doc.Project.AppendChild($ig)
  $doc.Save($csproj)
}

# Append Fire God T5 upgrade that adds a burn aura and visual transform
$upgSrc = Get-Content -Raw -LiteralPath $upg
if ($upgSrc -notmatch 'class\s+FireGod') {
  $append = @'

using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;

namespace LightningMonkey.Towers.Upgrades {
  public class FireGod : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {
    public override int Path => TOP; public override int Tier => 5; public override int Cost => 65000;
    public override string DisplayName => "Fire God";
    public override string Description => "Transforms into a blazing deity with a burning aura.";
    public override void ApplyUpgrade(TowerModel t) {
      // Visual transformation: imposing fire look
      var wiz520 = Game.instance.model.GetTowerFromId("WizardMonkey-520"); if (wiz520 != null) t.display = wiz520.display;
      t.displayScale *= 1.2f;

      // Passive burn aura: borrow Ring of Fire attack
      var tack400 = Game.instance.model.GetTowerFromId("TackShooter-400");
      if (tack400 != null) {
        var aura = tack400.GetAttackModel().Duplicate();
        aura.name = "FireGodAura";
        aura.range = t.range; // cover tower range
        var w = aura.weapons[0];
        w.Rate = 0.25f; // frequent ticks
        var proj = w.projectile;
        var dot = proj.GetBehavior<DamageOverTimeModel>();
        if (dot != null) {
          dot.damage = 3f;    // per tick
          dot.interval = 1f;  // per second
          dot.lifespan = 4f;  // lasts 4s
        }
        t.AddBehavior(aura);
      }
    }
  }
}
'@
  Add-Content -LiteralPath $upg -Value $append
}

Write-Host 'Applied custom assets and Fire God T5 upgrade.'

