$ErrorActionPreference = 'Stop'
$path = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey\Towers\Upgrades\AllUpgrades.cs'
$lines = @(
'using BTD_Mod_Helper.Api.Towers;',
'using BTD_Mod_Helper.Extensions;',
'using Il2CppAssets.Scripts.Models.Towers;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles;',
'using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;',
'using Il2CppAssets.Scripts.Unity;',
'',
'namespace LightningMonkey.Towers.Upgrades {',
'  // Top Path',
'  public class DarkBoost : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 1; public override int Cost => 250;',
'    public override string DisplayName => "Dark Boost"; public override string Description => "+1 damage, +2 pierce.";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      var p=t.GetAttackModel().weapons[0].projectile; p.GetDamageModel().damage+=1; p.pierce+=2;',
'      var wiz200 = Game.instance.model.GetTowerFromId("WizardMonkey-200"); if (wiz200 != null) t.display = wiz200.display;',
'    }',
'  }',
'  public class ShadowStrike : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 2; public override int Cost => 500;',
'    public override string DisplayName => "Shadow Strike"; public override string Description => "15% faster.";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      t.GetAttackModel().weapons[0].Rate *= 0.85f; var wiz300 = Game.instance.model.GetTowerFromId("WizardMonkey-300"); if (wiz300 != null) t.display = wiz300.display;',
'    }',
'  }',
'  public class VoidChanneling : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 3; public override int Cost => 1200;',
'    public override string DisplayName => "Void Channeling"; public override string Description => "+2 dmg, +3 pierce.";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      var p=t.GetAttackModel().weapons[0].projectile; p.GetDamageModel().damage+=2; p.pierce+=3;',
'      var wiz302 = Game.instance.model.GetTowerFromId("WizardMonkey-302"); if (wiz302 != null) t.display = wiz302.display;',
'    }',
'  }',
'  public class DarkMastery : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 4; public override int Cost => 9000;',
'    public override string DisplayName => "Dark Mastery"; public override string Description => "Bigger bolts and faster cast.";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      var a=t.GetAttackModel(); a.weapons[0].Rate *= 0.75f; a.weapons[0].projectile.scale *= 1.25f;',
'      var druid400 = Game.instance.model.GetTowerFromId("Druid-400"); if (druid400 != null) t.display = druid400.display;',
'    }',
'  }',
'  // T5 Fire God transformation (persistent burn aura)',
'  public class FireGod : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => TOP; public override int Tier => 5; public override int Cost => 65000;',
'    public override string DisplayName => "Fire God";',
'    public override string Description => "Transforms into a blazing deity with a persistent burning aura.";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      // Visual transformation',
'      var wiz520 = Game.instance.model.GetTowerFromId("WizardMonkey-520"); if (wiz520 != null) t.display = wiz520.display;',
'      t.displayScale *= 1.2f;',
'',
'      // Start from Inferno Ring aura for visuals/area denial',
'      var inferno = Game.instance.model.GetTowerFromId("TackShooter-520");',
'      if (inferno != null) {',
'        var aura = inferno.GetAttackModel().Duplicate();',
'        aura.name = "FireGodAura";',
'        aura.range = t.range;',
'        var w = aura.weapons[0];',
'        w.Rate = 0.25f; // frequent ticks keep DoT refreshed',
'        var proj = w.projectile;',
'        // Borrow Mortar 2xx burn model to ensure lingering DoT even after leaving aura',
'        var mortar200 = Game.instance.model.GetTowerFromId("MortarMonkey-200");',
'        if (mortar200 != null) {',
'          var mproj = mortar200.GetAttackModel().weapons[0].projectile;',
'          var mdot = mproj.GetBehavior<DamageOverTimeModel>();',
'          if (mdot != null) {',
'            var fireDot = mdot.Duplicate();',
'            fireDot.name = "FireGodBurn";',
'            fireDot.damage = 4f;    // damage per second',
'            fireDot.interval = 1f;  // tick every second',
'            fireDot.lifespan = 4f;  // lasts 4 seconds, refreshes on re-entry',
'            proj.AddBehavior(fireDot);',
'          }',
'        }',
'        t.AddBehavior(aura);',
'      }',
'    }',
'  }',
'',
'  // Middle Path',
'  public class QuickCast : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => MIDDLE; public override int Tier => 1; public override int Cost => 150;',
'    public override string DisplayName => "Quick Cast"; public override string Description => "15% faster";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      t.GetAttackModel().weapons[0].Rate *= 0.85f; var druid100 = Game.instance.model.GetTowerFromId("Druid-100"); if (druid100 != null) t.display = druid100.display;',
'    }',
'  }',
'  public class SurgeCasting : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => MIDDLE; public override int Tier => 2; public override int Cost => 300;',
'    public override string DisplayName => "Surge Casting"; public override string Description => "20% faster";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      t.GetAttackModel().weapons[0].Rate *= 0.8f; var druid200 = Game.instance.model.GetTowerFromId("Druid-200"); if (druid200 != null) t.display = druid200.display;',
'    }',
'  }',
'  public class ChainLightning : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => MIDDLE; public override int Tier => 3; public override int Cost => 1500;',
'    public override string DisplayName => "Chain Lightning";',
'    public override void ApplyUpgrade(TowerModel towerModel) {',
'      var proj = towerModel.GetAttackModel().weapons[0].projectile;',
'      var lm = proj.GetBehavior<LightningModel>();',
'      if (lm != null) { lm.splits += 12; lm.splitRange += 18f; } else {',
'        var druid200 = Game.instance.model.GetTowerFromId("Druid-200");',
'        var src = druid200.GetAttackModel().weapons[0].projectile.GetBehavior<LightningModel>().Duplicate();',
'        proj.AddBehavior(src);',
'      }',
'      proj.pierce += 2; proj.GetDamageModel().damage += 1;',
'      var druid300 = Game.instance.model.GetTowerFromId("Druid-300"); if (druid300 != null) towerModel.display = druid300.display;',
'    }',
'  }',
'',
'  // Bottom Path',
'  public class ExtendedRange : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => BOTTOM; public override int Tier => 1; public override int Cost => 180;',
'    public override string DisplayName => "Extended Range"; public override string Description => "+10 range";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      t.range += 10f; var druid010 = Game.instance.model.GetTowerFromId("Druid-010"); if (druid010 != null) t.display = druid010.display;',
'    }',
'  }',
'  public class PrecisionStrike : ModUpgrade<LightningMonkey.Towers.LightningMonkeyTower> {',
'    public override int Path => BOTTOM; public override int Tier => 2; public override int Cost => 350;',
'    public override string DisplayName => "Precision Strike"; public override string Description => "+1 dmg, +8 range";',
'    public override void ApplyUpgrade(TowerModel t) {',
'      t.range += 8f; t.GetAttackModel().weapons[0].projectile.GetDamageModel().damage += 1; var druid020 = Game.instance.model.GetTowerFromId("Druid-020"); if (druid020 != null) t.display = druid020.display;',
'    }',
'  }',
'}'
)
[IO.File]::WriteAllLines($path, $lines)
Write-Host 'AllUpgrades.cs updated with real Fire God DoT implementation.'

