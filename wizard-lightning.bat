@echo off
echo Creating <PERSON> Monkey based on Wizard for better lightning effects...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "TOWER_FILE=%PROJ_DIR%\Towers\LightningMonkey.cs"

echo Writing Lightning Monkey with Wizard base for magical lightning...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.TowerSets;
echo.
echo namespace LightningMonkey.Towers {
echo   public class LightningMonkeyTower : ModTower {
echo     public override string BaseTower =^> TowerType.WizardMonkey;
echo     public override TowerSet TowerSet =^> TowerSet.Magic;
echo.
echo     public override string DisplayName =^> "Lightning Monkey";
echo     public override string Description =^> "A mystical monkey wielding crackling lightning! Shoots electric bolts that chain through bloons with devastating power.";
echo     public override int Cost =^> 550;
echo.
echo     public override int TopPathUpgrades =^> 5;
echo     public override int MiddlePathUpgrades =^> 5;
echo     public override int BottomPathUpgrades =^> 5;
echo.
echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
echo       m.range += 15f;
echo       var atk = m.GetAttackModel^(^);
echo       atk.weapons[0].Rate = 0.8f;
echo       var p = atk.weapons[0].projectile;
echo       p.pierce += 3;
echo       p.GetDamageModel^(^).damage = 3;
echo       p.scale = 1.4f;
echo     }
echo   }
echo }
) > "%TOWER_FILE%"

echo Building Lightning Monkey with Wizard base...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey with Wizard base!
    echo ========================================
    echo.
    echo Visual improvements:
    echo - Based on Wizard Monkey ^(magical monkey with staff^)
    echo - Uses Wizard's magical projectiles ^(closer to lightning^)
    echo - Enhanced stats: +15 range, 3 damage, +3 pierce
    echo - Larger projectiles ^(1.4x scale^)
    echo - Faster attack rate ^(0.8s^)
    echo.
    echo The Wizard base gives you:
    echo - A monkey with a magical staff
    echo - Magical projectile effects
    echo - Mystical appearance
    echo - Better foundation for lightning powers
    echo.
    echo NOTE: For the exact cyan lightning appearance from your image,
    echo you would need custom 3D models and textures, which requires
    echo advanced modding tools beyond basic BTD6 Mod Helper.
    echo.
    echo Launch BTD6 to test this version!
    echo ^(Look in Magic category, cost 550^)
) else (
    echo Build failed. Check errors above.
)
pause
