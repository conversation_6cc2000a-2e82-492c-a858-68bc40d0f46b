$ErrorActionPreference = 'Stop'
$csproj = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey\LightningMonkey.csproj'
if (-not (Test-Path -LiteralPath $csproj)) { throw "csproj not found: $csproj" }
[xml]$doc = Get-Content -Raw -LiteralPath $csproj
# Ensure an AutoEmbed property is present and set to false to avoid duplication with btd6.targets
$pgNodes = @($doc.Project.PropertyGroup)
if ($pgNodes.Count -eq 0) { throw 'No PropertyGroup found in csproj' }
$pg = $pgNodes[0]
$auto = $pg.SelectSingleNode('AutoEmbed')
if ($null -eq $auto) {
  $auto = $doc.CreateElement('AutoEmbed')
  $auto.InnerText = 'false'
  [void]$pg.AppendChild($auto)
} else {
  $auto.InnerText = 'false'
}
$doc.Save($csproj)
Write-Host 'Set AutoEmbed=false in csproj.'
