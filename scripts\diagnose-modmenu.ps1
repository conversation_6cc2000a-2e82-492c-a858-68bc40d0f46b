$ErrorActionPreference = 'SilentlyContinue'
Write-Host '===== Diagnose Mod Menu / Helper ====='
$root = (Get-Location).Path
Write-Host "Current dir: $root"

$mods = Join-Path $root 'Mods'
if (Test-Path $mods) {
  Write-Host "- Mods folder found: $mods"
  $helpers = Get-ChildItem -Path $mods -Filter 'BloonsTD6*Helper*.dll' -File -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending
  if ($helpers) {
    Write-Host "- Helper DLLs (newest first):"
    $helpers | ForEach-Object { Write-Host ('  {0}  ({1:g})' -f $_.FullName, $_.LastWriteTime) }
  } else {
    Write-Host "- No Mod Helper DLL found in Mods"
  }
} else {
  Write-Host "- Mods folder NOT found here."
}

$log = Join-Path $root 'MelonLoader/Latest.log'
if (Test-Path $log) {
  Write-Host "- Reading MelonLoader/Latest.log"
  $lines = Get-Content -Path $log -TotalCount 200
  $ver = $lines | Select-String -Pattern 'MelonLoader v'
  $game = $lines | Select-String -Pattern 'Game Ver|Game Version|Version:' | Select-Object -First 1
  $helper = $lines | Select-String -Pattern 'BTD6 Mod Helper'
  if ($ver) { Write-Host ('  {0}' -f $ver.Line) }
  if ($game) { Write-Host ('  {0}' -f $game.Line) }
  if ($helper) {
    Write-Host '  Helper lines:'
    $helper | ForEach-Object { Write-Host ('    {0}' -f $_.Line) }
  } else {
    Write-Host '  (No BTD6 Mod Helper lines found in Latest.log)'
  }
} else {
  Write-Host '- MelonLoader/Latest.log not found (launch the game once to create it)'
}

Write-Host '====================================================='

