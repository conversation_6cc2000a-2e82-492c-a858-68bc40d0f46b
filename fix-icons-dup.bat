@echo off
setlocal ENABLEDELAYEDEXPANSION

echo Fixing duplicate EmbeddedResource items by disabling SDK default includes...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "CSPROJ=%PROJ_DIR%\LightningMonkey.csproj"

if not exist "%CSPROJ%" (
  echo Could not find project file: %CSPROJ%
  echo Make sure the mod project exists and try again.
  pause
  exit /b 1
)

powershell -NoProfile -Command "$p='%CSPROJ%';$s=Get-Content -Raw $p; if($s -notmatch 'EnableDefaultEmbeddedResourceItems'){ $s = $s -replace '(<Project[^>]*>)', '$1`r`n  <PropertyGroup>`r`n    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>`r`n  </PropertyGroup>'; Set-Content -Path $p -Value $s; Write-Host 'Added EnableDefaultEmbeddedResourceItems=false to csproj'; } else { Write-Host 'EnableDefaultEmbeddedResourceItems already present'; }"

cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

if %ERRORLEVEL% EQU 0 (
  echo ========================================
  echo SUCCESS! Duplicate resource fix applied and build succeeded.
  echo ========================================
) else (
  echo Build failed. If duplicate resource error persists, please share the first 30 lines of LightningMonkey.csproj.
)

endlocal
pause
