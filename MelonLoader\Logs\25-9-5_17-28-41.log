
[17:28:41.866] ------------------------------
[17:28:41.868] MelonLoader v0.7.1 Open-Beta
[17:28:41.870] OS: Windows 11
[17:28:41.871] Hash Code: E02D6B4281E511A9F1EA88CD1737B993A6B9C7C5566EFBE68300B1FCF1479C14
[17:28:41.872] ------------------------------
[17:28:41.872] Game Type: Il2cpp
[17:28:41.872] Game Arch: x64
[17:28:41.873] ------------------------------
[17:28:41.873] Command-Line: 
[17:28:41.873] ------------------------------
[17:28:41.873] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:28:41.873] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[17:28:41.874] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[17:28:41.874] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[17:28:41.874] Runtime Type: net6
[17:28:41.936] ------------------------------
[17:28:41.936] Game Name: BloonsTD6
[17:28:41.937] Game Developer: Ninja Kiwi
[17:28:41.938] Unity Version: 6000.0.52f1
[17:28:41.938] Game Version: 50.1
[17:28:41.938] ------------------------------

[17:28:42.329] Preferences Loaded!

[17:28:42.340] Loading UserLibs...
[17:28:42.343] 0 UserLibs loaded.

[17:28:42.343] Loading Plugins...
[17:28:42.347] 0 Plugins loaded.

[17:28:43.044] Loading Il2CppAssemblyGenerator...
[17:28:43.088] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[17:28:43.303] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[17:28:43.303] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[17:28:43.303] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[17:28:43.303] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[17:28:43.311] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[17:28:43.311] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.5.0-ci.625+51abbdd5e95447d4450eb82bde1b77ecff3cea74
[17:28:43.311] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.52
[17:28:43.311] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[17:28:43.312] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[17:28:43.312] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[17:28:43.312] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[17:28:43.312] [Il2CppAssemblyGenerator] Checking GameAssembly...
[17:28:43.480] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[17:28:43.481] Loading Mods...
[17:28:43.516] ------------------------------
[17:28:43.556] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[17:28:43.556] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[17:28:43.558] Melon Assembly loaded: '.\Mods\LightningMonkey.dll'
[17:28:43.558] SHA256 Hash: 'A79FD186CB7383CFBC2DCA7610BD29FE086A70987EEE55D101D6BA45F93B96F9'

[17:28:44.119] ------------------------------
[17:28:44.119] BloonsTD6 Mod Helper v3.4.12
[17:28:44.120] by Gurrenm4 and Doombubbles
[17:28:44.120] Assembly: Btd6ModHelper.dll
[17:28:44.120] ------------------------------
[17:28:44.122] ------------------------------
[17:28:44.122] Lightning Monkey Mod v1.0.0
[17:28:44.122] by You
[17:28:44.122] Assembly: LightningMonkey.dll
[17:28:44.122] ------------------------------
[17:28:44.122] ------------------------------
[17:28:44.122] 2 Mods loaded.

[17:28:45.153] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[17:28:45.319] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[17:28:45.343] [Il2CppInterop] Registered mono type MelonLoader.Support.MonoEnumeratorWrapper in il2cpp domain
[17:28:45.348] [Il2CppInterop] Registered mono type MelonLoader.Support.SM_Component in il2cpp domain
[17:28:45.361] [Il2CppICallInjector] Registered mono icall UnityEngine.Transform::SetAsLastSibling in il2cpp domain
[17:28:45.363] Support Module Loaded: C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll
[17:28:45.778] AccessTools.Method: Could not find method for type Il2CppAssets.Scripts.Unity.UI_New.Main.MainMenu and name Start and parameters 
[17:28:46.241] [BloonsTD6_Mod_Helper] System.NullReferenceException: Object reference not set to an instance of an object.
   at BTD_Mod_Helper.Api.ModMenu.ModHelperHttp.DownloadFile(String url, String filePath)
[17:28:46.486] [BloonsTD6_Mod_Helper] Downloaded Btd6ModHelper.xml for v3.4.12
[17:28:53.565] [BloonsTD6_Mod_Helper] Finished getting mods from github in background, found 382 mods over 7.3 seconds
[17:28:55.855] [BloonsTD6_Mod_Helper] Registering ModContent for BloonsTD6 Mod Helper...
[17:28:55.859] [BloonsTD6_Mod_Helper] Registering ModContent for Lightning Monkey Mod...
[17:35:34.070] Preferences Saved!
