@echo off
setlocal ENABLEDELAYEDEXPANSION

echo Adding upgrade icons and improving black hole (stationary vortex)...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"
set "RES_DIR=%PROJ_DIR%\Resources"

if not exist "%RES_DIR%" mkdir "%RES_DIR%"

REM 1) Update code generator script to include icons and a stationary black hole
REM    We will overwrite AllUpgrades.cs via lightning-staff-complete.bat, so patch that script first.

for /f "tokens=* delims=" %%A in ('type lightning-staff-complete.bat') do (
  set "line=%%A"
  echo(!line!>> "%cd%\_tmp_lsc_edit.bat"
)

REM Replace the ChainLightning block to include Icon and keep chaining
powershell -Command "(Get-Content -Raw 'lightning-staff-complete.bat') -replace 'public override string Description =\^> \"Bolts arc between nearby bloons, chaining several times.\";','public override string Description => "Bolts arc between nearby bloons, chaining several times."; public override string Icon => "ChainLightning_Icon";' | Set-Content 'lightning-staff-complete.bat'"

REM Inject icons into top/middle/bottom inline classes and make the T5 vortex stationary
powershell -Command "^$
$path = 'lightning-staff-complete.bat';
$t = Get-Content -Raw $path;
$t = $t -replace 'Dark Boost\"; public override string Description =\^> \"\+1 damage, \+2 pierce\.\";','Dark Boost"; public override string Description => "+1 damage, +2 pierce."; public override string Icon => "DarkBoost_Icon";';
$t = $t -replace 'Shadow Strike\"; public override string Description =\^> \"15%% faster\.\";','Shadow Strike"; public override string Description => "15% faster."; public override string Icon => "ShadowStrike_Icon";';
$t = $t -replace 'Void Channeling\"; public override string Description =\^> \"\+2 dmg, \+3 pierce\.\";','Void Channeling"; public override string Description => "+2 dmg, +3 pierce."; public override string Icon => "VoidChanneling_Icon";';
$t = $t -replace 'Dark Mastery\"; public override string Description =\^> \"Bigger bolts and faster cast\.\";','Dark Mastery"; public override string Description => "Bigger bolts and faster cast."; public override string Icon => "DarkMastery_Icon";';
$t = $t -replace 'Quick Cast\"; public override string Description =\^> \"15%% faster\";','Quick Cast"; public override string Description => "15% faster"; public override string Icon => "QuickCast_Icon";';
$t = $t -replace 'Surge Casting\"; public override string Description =\^> \"20%% faster\";','Surge Casting"; public override string Description => "20% faster"; public override string Icon => "SurgeCasting_Icon";';
$t = $t -replace 'Extended Range\"; public override string Description =\^> \"\+10 range\";','Extended Range"; public override string Description => "+10 range"; public override string Icon => "ExtendedRange_Icon";';
$t = $t -replace 'Precision Strike\"; public override string Description =\^> \"\+1 dmg, \+8 range\";','Precision Strike"; public override string Description => "+1 dmg, +8 range"; public override string Icon => "PrecisionStrike_Icon";';

# Make black hole stationary by setting tornado TravelStraitModel speed to 0 and increase scale
$t = $t -replace 'var tornado = druid005.GetAttackModel\^\(\^\)\.weapons\[0\]\.projectile\.Duplicate\^\(\^\);\r?\n\s*tornado\.pierce = 999f;','var tornado = druid005.GetAttackModel().weapons[0].projectile.Duplicate();\n      var travel = tornado.GetBehavior<TravelStraitModel>(); if (travel != null) travel.speed = 0f;\n      tornado.pierce = 999f;';
$t = $t -replace 'tornado\.scale \*= 1\.8f;','tornado.scale *= 2.2f;';

Set-Content -Path $path -Value $t;"

REM 2) Create tiny placeholder PNG icons for each upgrade name
set ICONS=DarkBoost_Icon ShadowStrike_Icon VoidChanneling_Icon DarkMastery_Icon VoidStormMaster_Icon ChainLightning_Icon QuickCast_Icon SurgeCasting_Icon ExtendedRange_Icon PrecisionStrike_Icon

for %%I in (%ICONS%) do (
  > "%RES_DIR%\%%I.b64" echo iVBORw0KGgoAAAANSUhEUgAAAEAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsSAAALEgHS3X78AAAAB3RJTUUH5QYJFCIV6b2uJwAAAB1pVFh0Q29tbWVudAAAAAAAQ3JlYXRlZCBieSBBdWdNRU5UAAAAG0lEQVRIx2NgGAWjYBSMglEwCjA0wP///w8DAwMDAwA1WgF7b3d0LwAAAABJRU5ErkJggg==
  certutil -f -decode "%RES_DIR%\%%I.b64" "%RES_DIR%\%%I.png" >nul 2>&1
  del /q "%RES_DIR%\%%I.b64" >nul 2>&1
)

REM 3) Re-generate code files and build
cmd /c lightning-staff-complete.bat

endlocal

