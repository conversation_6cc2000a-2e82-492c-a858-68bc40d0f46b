@echo off
echo Fixing upgrade conflicts and creating clean structure...

set "PROJ_DIR=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
set "UPGRADES_DIR=%PROJ_DIR%\Towers\Upgrades"

echo Removing conflicting upgrade files...
if exist "%UPGRADES_DIR%" rmdir /s /q "%UPGRADES_DIR%"
mkdir "%UPGRADES_DIR%"

echo Creating clean Chain Lightning upgrade (Middle T3)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class ChainLightning : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 3;
echo     public override int Cost =^> 1400;
echo.
echo     public override string DisplayName =^> "Chain Lightning";
echo     public override string Description =^> "Lightning bolts chain between nearby bloons with crackling electrical arcs!";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo.
echo         var chainProjectile = projectile.Duplicate^(^);
echo         chainProjectile.pierce = 3f;
echo         chainProjectile.GetDamageModel^(^).damage = 3f;
echo         chainProjectile.scale = 0.9f;
echo.
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo           "ChainLightning",
echo           chainProjectile,
echo           new SingleEmissionModel^("", null^),
echo           true, false, false
echo         ^)^);
echo.
echo         projectile.pierce += 3f;
echo         projectile.GetDamageModel^(^).damage += 2f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\ChainLightning.cs"

echo Creating Void Storm Master upgrade (Top T5)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles;
echo using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
echo using Il2CppAssets.Scripts.Models.Towers.Behaviors.Emissions;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class VoidStormMaster : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 5;
echo     public override int Cost =^> 45000;
echo.
echo     public override string DisplayName =^> "Void Storm Master";
echo     public override string Description =^> "DARK MAGIC: Warps reality to create devastating black holes that consume bloons into the void! Lightning becomes infused with dark energy.";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo.
echo         projectile.pierce = 15f;
echo         projectile.GetDamageModel^(^).damage = 25f;
echo         projectile.scale = 2.0f;
echo.
echo         var blackHoleProjectile = projectile.Duplicate^(^);
echo         blackHoleProjectile.pierce = 999f;
echo         blackHoleProjectile.GetDamageModel^(^).damage = 50f;
echo         blackHoleProjectile.scale = 3.0f;
echo.
echo         projectile.AddBehavior^(new CreateProjectileOnContactModel^(
echo           "BlackHoleVoid",
echo           blackHoleProjectile,
echo           new SingleEmissionModel^("", null^),
echo           true, false, false
echo         ^)^);
echo.
echo         attackModel.weapons[0].Rate = 0.3f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\VoidStormMaster.cs"

echo Creating Lightning Boost upgrade (Top T1)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class LightningBoost : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> TOP;
echo     public override int Tier =^> 1;
echo     public override int Cost =^> 200;
echo.
echo     public override string DisplayName =^> "Lightning Boost";
echo     public override string Description =^> "Increased lightning damage and pierce.";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         var projectile = attackModel.weapons[0].projectile;
echo         projectile.pierce += 2f;
echo         projectile.GetDamageModel^(^).damage += 1f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\LightningBoost.cs"

echo Creating Quick Cast upgrade (Middle T1)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class QuickCast : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> MIDDLE;
echo     public override int Tier =^> 1;
echo     public override int Cost =^> 150;
echo.
echo     public override string DisplayName =^> "Quick Cast";
echo     public override string Description =^> "Faster lightning casting speed.";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       var attackModel = towerModel.GetAttackModel^(^);
echo       if ^(attackModel != null^) {
echo         attackModel.weapons[0].Rate *= 0.85f;
echo       }
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\QuickCast.cs"

echo Creating Extended Range upgrade (Bottom T1)...
(
echo using BTD_Mod_Helper.Api.Towers;
echo using BTD_Mod_Helper.Extensions;
echo using Il2CppAssets.Scripts.Models.Towers;
echo.
echo namespace LightningMonkey.Towers.Upgrades {
echo   public class ExtendedRange : ModUpgrade^<LightningMonkeyTower^> {
echo     public override int Path =^> BOTTOM;
echo     public override int Tier =^> 1;
echo     public override int Cost =^> 180;
echo.
echo     public override string DisplayName =^> "Extended Range";
echo     public override string Description =^> "Magical staff extends lightning reach.";
echo.
echo     public override void ApplyUpgrade^(TowerModel towerModel^) {
echo       towerModel.range += 10f;
echo     }
echo   }
echo }
) > "%UPGRADES_DIR%\ExtendedRange.cs"

echo Building clean Dark Lightning Monkey...
cd /d "%PROJ_DIR%"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Clean Dark Lightning Monkey!
    echo ========================================
    echo.
    echo FIXED UPGRADE STRUCTURE:
    echo - Removed conflicting upgrade files
    echo - Clean tier system with no conflicts
    echo.
    echo UPGRADES AVAILABLE:
    echo TOP PATH:
    echo   T1: Lightning Boost - +1 damage, +2 pierce ^(200^)
    echo   T5: Void Storm Master - BLACK HOLE MAGIC! ^(45000^)
    echo.
    echo MIDDLE PATH:
    echo   T1: Quick Cast - 15%% faster attacks ^(150^)
    echo   T3: Chain Lightning - Lightning chains! ^(1400^)
    echo.
    echo BOTTOM PATH:
    echo   T1: Extended Range - +10 range ^(180^)
    echo.
    echo Launch BTD6 to test your Dark Lightning Monkey!
    echo ^(Magic category, 750 cost^)
) else (
    echo Build failed. Check errors above.
)
pause
