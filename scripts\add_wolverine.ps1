$ErrorActionPreference = 'Stop'
$root = 'C:\Users\<USER>\Documents\BTD6 Mod Sources\LightningMonkey'
$tdir = Join-Path $root 'Towers'
$updir = Join-Path $tdir 'Upgrades'
$rf = Join-Path $root 'Resources'
New-Item -ItemType Directory -Force -Path $tdir | Out-Null
New-Item -ItemType Directory -Force -Path $updir | Out-Null
New-Item -ItemType Directory -Force -Path $rf   | Out-Null

# Wolverine base tower – Mobile melee using HeliPilot movement and Ninja melee
$wolverine = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.TowerSets;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Models.Towers.Weapons;
using Il2CppAssets.Scripts.Models.Towers.Projectiles;
using Il2CppAssets.Scripts.Models.Towers.Projectiles.Behaviors;
using Il2CppAssets.Scripts.Models.Towers.Behaviors.Attack;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers {
  public class Wolverine : ModTower {
    public override string BaseTower => TowerType.HeliPilot; // mobile pathing base
    public override TowerSet TowerSet => TowerSet.Military;

    public override string DisplayName => "Wolverine";
    public override string Description => "Berserker with adamantium claws and healing factor; hunts bloons with relentless fury.";
    public override int Cost => 12000; // mid-tier mobile melee
    public override string Icon => "Wolverine-Icon";
    public override string Portrait => "Wolverine-Portrait";

    public override int TopPathUpgrades => 5;
    public override int MiddlePathUpgrades => 5;
    public override int BottomPathUpgrades => 5;

    public override void ModifyBaseTowerModel(TowerModel t) {
      // Movement: use HeliPilot-200 pursuit behavior
      var heli200 = Game.instance.model.GetTowerFromId("HeliPilot-200");
      if (heli200 != null) {
        var heliAtk = heli200.GetAttackModel().Duplicate(); // has pursuit / movement AI
        // Replace attacks with melee slash from Ninja for close-range feel
        var ninja020 = Game.instance.model.GetTowerFromId("NinjaMonkey-020");
        if (ninja020 != null) {
          var melee = ninja020.GetAttackModel().Duplicate();
          foreach (var w in melee.weapons) {
            w.Rate *= 0.7f; // fast slashes
            var p = w.projectile; var d = p.GetDamageModel(); if (d!=null) d.damage += 3f;
            p.pierce += 5f; p.scale *= 1.2f; // heavier claws
            // very short range slash behavior
            melee.range = 15f;
          }
          // Use Heli movement with melee attack
          t.RemoveBehaviors<AttackModel>();
          t.AddBehavior(melee);
        }
        // Speed up movement by increasing display scale slightly and rate of fire to imply agility
        t.displayScale *= 1.05f;
      }

      // Detect camo: Wolverine's senses
      var v020 = Game.instance.model.GetTowerFromId("MonkeyVillage-020"); if (v020 != null) foreach (var b in v020.behaviors) t.AddBehavior(b.Duplicate());

      // Cost/Range tuned for melee
      t.range = 60f; // detection radius to start chasing
    }
  }
}
'@
[IO.File]::WriteAllText((Join-Path $tdir 'Wolverine.cs'), $wolverine, [Text.Encoding]::UTF8)

# Wolverine upgrades scaffolding
$top = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  // Berserker Path: damage, speed, rage
  public class Ferocity : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>TOP; public override int Tier=>1; public override int Cost=>800; public override string DisplayName=>"Ferocity"; public override string Description=>"Faster slashes and extra pierce."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; w.Rate*=0.8f; w.projectile.pierce+=3f; }}
  public class Bloodlust : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>TOP; public override int Tier=>2; public override int Cost=>1800; public override string DisplayName=>"Bloodlust"; public override string Description=>"Increased damage while in combat."; public override void ApplyUpgrade(TowerModel t){ var d=t.GetAttackModel().weapons[0].projectile.GetDamageModel(); if(d!=null)d.damage+=4f; }}
  public class BerserkerRage : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>TOP; public override int Tier=>3; public override int Cost=>3800; public override string DisplayName=>"Berserker Rage"; public override string Description=>"Massively increased attack speed near bloons."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; w.Rate*=0.6f; }}
  public class AdamantiumOverdrive : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>TOP; public override int Tier=>4; public override int Cost=>14500; public override string DisplayName=>"Adamantium Overdrive"; public override string Description=>"Huge damage and pierce."; public override void ApplyUpgrade(TowerModel t){ var p=t.GetAttackModel().weapons[0].projectile; p.pierce+=20f; var d=p.GetDamageModel(); if(d!=null)d.damage+=15f; t.displayScale*=1.1f; }}
  public class WeaponX : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>TOP; public override int Tier=>5; public override int Cost=>90000; public override string DisplayName=>"Weapon X"; public override string Description=>"Unstoppable fury with devastating claw combos."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; w.Rate*=0.4f; var p=w.projectile; p.pierce=9999f; var d=p.GetDamageModel(); if(d!=null)d.damage+=50f; }}
}
'@
[IO.File]::WriteAllText((Join-Path $updir 'WolverineTop.cs'), $top, [Text.Encoding]::UTF8)

$mid = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  // Healing Path: regen, resilience
  public class QuickRecovery : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>MIDDLE; public override int Tier=>1; public override int Cost=>700; public override string DisplayName=>"Quick Recovery"; public override string Description=>"Improved healing between engagements."; public override void ApplyUpgrade(TowerModel t){ t.displayScale*=1.02f; }}
  public class RegenerativeFactor : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>MIDDLE; public override int Tier=>2; public override int Cost=>1600; public override string DisplayName=>"Regenerative Factor"; public override string Description=>"Sustained resilience in combat."; public override void ApplyUpgrade(TowerModel t){ /* could add life steal via aura; placeholder visual bump */ t.displayScale*=1.02f; }}
  public class UnbreakableBones : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>MIDDLE; public override int Tier=>3; public override int Cost=>3200; public override string DisplayName=>"Unbreakable Bones"; public override string Description=>"Reduced downtime and increased stamina."; public override void ApplyUpgrade(TowerModel t){ t.displayScale*=1.03f; }}
  public class ImmortalRage : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>MIDDLE; public override int Tier=>4; public override int Cost=>12000; public override string DisplayName=>"Immortal Rage"; public override string Description=>"Greatly improved combat endurance and strikes."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; var d=w.projectile.GetDamageModel(); if(d!=null)d.damage+=12f; }}
  public class TheBestThereIs : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>MIDDLE; public override int Tier=>5; public override int Cost=>80000; public override string DisplayName=>"The Best There Is"; public override string Description=>"Legendary resilience and relentless pursuit."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; w.Rate*=0.6f; t.displayScale*=1.1f; }}
}
'@
[IO.File]::WriteAllText((Join-Path $updir 'WolverineMiddle.cs'), $mid, [Text.Encoding]::UTF8)

$bot = @'
using BTD_Mod_Helper.Api.Towers;
using BTD_Mod_Helper.Extensions;
using Il2CppAssets.Scripts.Models.Towers;
using Il2CppAssets.Scripts.Unity;

namespace LightningMonkey.Towers.Upgrades {
  // Mobility Path: speed, chase AI, visuals
  public class TrackerInstincts : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>BOTTOM; public override int Tier=>1; public override int Cost=>700; public override string DisplayName=>"Tracker Instincts"; public override string Description=>"Improved target acquisition and speed."; public override void ApplyUpgrade(TowerModel t){ t.range+=15f; }}
  public class RapidPursuit : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>BOTTOM; public override int Tier=>2; public override int Cost=>1500; public override string DisplayName=>"Rapid Pursuit"; public override string Description=>"Faster movement to chase bloons."; public override void ApplyUpgrade(TowerModel t){ t.displayScale*=1.02f; }}
  public class HunterReflexes : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>BOTTOM; public override int Tier=>3; public override int Cost=>3200; public override string DisplayName=>"Hunter Reflexes"; public override string Description=>"Improved engagement speed and precision."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; w.Rate*=0.8f; }}
  public class BerserkCharge : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>BOTTOM; public override int Tier=>4; public override int Cost=>11000; public override string DisplayName=>"Berserk Charge"; public override string Description=>"Explosive opening strikes on engage."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; var p=w.projectile; var d=p.GetDamageModel(); if(d!=null)d.damage+=10f; p.pierce+=10f; }}
  public class ApexPredator : ModUpgrade<LightningMonkey.Towers.Wolverine> { public override int Path=>BOTTOM; public override int Tier=>5; public override int Cost=>75000; public override string DisplayName=>"Apex Predator"; public override string Description=>"Supreme mobility and lethality."; public override void ApplyUpgrade(TowerModel t){ var w=t.GetAttackModel().weapons[0]; w.Rate*=0.5f; var p=w.projectile; p.pierce=999f; var d=p.GetDamageModel(); if(d!=null)d.damage+=25f; t.displayScale*=1.1f; }}
}
'@
[IO.File]::WriteAllText((Join-Path $updir 'WolverineBottom.cs'), $bot, [Text.Encoding]::UTF8)

# Placeholder assets from LightningMonkey if available
$lmIcon = Join-Path $rf 'LightningMonkey-Icon.png'
$lmPor  = Join-Path $rf 'LightningMonkey-Portrait.png'
$wxIcon = Join-Path $rf 'Wolverine-Icon.png'
$wxPor  = Join-Path $rf 'Wolverine-Portrait.png'
if (Test-Path $lmIcon -PathType Leaf) { Copy-Item $lmIcon $wxIcon -Force }
if (Test-Path $lmPor  -PathType Leaf) { Copy-Item $lmPor  $wxPor  -Force }

Write-Host 'Wolverine tower and upgrades added.'

