{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/win-x64": {"MelonLoader.NativeHost/0.7.1": {"dependencies": {"LavaGang.MelonLoader": "0.7.1", "MelonLoader": "*******"}, "runtime": {"MelonLoader.NativeHost.dll": {}}}, "LavaGang.MelonLoader/0.7.1": {"runtime": {"MelonLoader.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MelonLoader/*******": {"runtime": {"MelonLoader.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "0Harmony/********": {"runtime": {"0Harmony.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MonoMod.Utils/*********": {"runtime": {"MonoMod.Utils.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "MonoMod.RuntimeDetour/*********": {"runtime": {"MonoMod.RuntimeDetour.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "bHapticsLib/*******": {"runtime": {"bHapticsLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AssetRipper.Primitives/*******": {"runtime": {"AssetRipper.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Tomlet/*******": {"runtime": {"Tomlet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mono.Cecil/********": {"runtime": {"Mono.Cecil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AssetsTools.NET/*******": {"runtime": {"AssetsTools.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver.DotNet/*******": {"runtime": {"AsmResolver.DotNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver.PE/*******": {"runtime": {"AsmResolver.PE.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver/*******": {"runtime": {"AsmResolver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.Generator/*******": {"runtime": {"Il2CppInterop.Generator.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.Runtime/*******": {"runtime": {"Il2CppInterop.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Diagnostics.Runtime/3.1.10.12801": {"runtime": {"Microsoft.Diagnostics.Runtime.dll": {"assemblyVersion": "3.1.10.12801", "fileVersion": "3.1.10.12801"}}}, "Mono.Cecil.Mdb/********": {"runtime": {"Mono.Cecil.Mdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Pdb/********": {"runtime": {"Mono.Cecil.Pdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Rocks/********": {"runtime": {"Mono.Cecil.Rocks.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AsmResolver.PE.File/*******": {"runtime": {"AsmResolver.PE.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.HarmonySupport/*******": {"runtime": {"Il2CppInterop.HarmonySupport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "WebSocketDotNet/1.0.0.0": {"runtime": {"WebSocketDotNet.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Il2CppInterop.Common/*******": {"runtime": {"Il2CppInterop.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MonoMod.Backports/1.1.2.0": {"runtime": {"MonoMod.Backports.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}}, "Microsoft.Extensions.Logging.Abstractions/*******": {"runtime": {"Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Diagnostics.NETCore.Client/0.2.8.10101": {"runtime": {"Microsoft.Diagnostics.NETCore.Client.dll": {"assemblyVersion": "0.2.8.10101", "fileVersion": "0.2.8.10101"}}}, "MonoMod.ILHelpers/*******": {"runtime": {"MonoMod.ILHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"MelonLoader.NativeHost/0.7.1": {"type": "project", "serviceable": false, "sha512": ""}, "LavaGang.MelonLoader/0.7.1": {"type": "project", "serviceable": false, "sha512": ""}, "MelonLoader/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "0Harmony/********": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.Utils/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.RuntimeDetour/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "bHapticsLib/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetRipper.Primitives/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Tomlet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil/********": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetsTools.NET/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.DotNet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.PE/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Generator/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Runtime/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Diagnostics.Runtime/3.1.10.12801": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Mdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Pdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Rocks/********": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.PE.File/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.HarmonySupport/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "WebSocketDotNet/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.Backports/1.1.2.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Diagnostics.NETCore.Client/0.2.8.10101": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.ILHelpers/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}