@echo off
echo Adding Game class using statement...

set "TOWER_FILE=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\LightningMonkey.cs"
set "FORKED_FILE=%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey\Towers\Upgrades\Mid_T3_ForkedLightning.cs"

echo Fixing tower file...
powershell -NoProfile -Command "$content = Get-Content '%TOWER_FILE%' -Raw; if ($content -notmatch 'using Il2CppAssets.Scripts.Unity.UI_New.InGame') { $content = $content -replace 'using Il2CppAssets.Scripts.Models.TowerSets;', 'using Il2CppAssets.Scripts.Models.TowerSets;`nusing Il2CppAssets.Scripts.Unity.UI_New.InGame;' }; $content | Set-Content '%TOWER_FILE%'"

echo Fixing forked lightning upgrade...
powershell -NoProfile -Command "$content = Get-Content '%FORKED_FILE%' -Raw; if ($content -notmatch 'using Il2CppAssets.Scripts.Unity.UI_New.InGame') { $content = $content -replace 'using Il2CppAssets.Scripts.Models.Towers;', 'using Il2CppAssets.Scripts.Models.Towers;`nusing Il2CppAssets.Scripts.Unity.UI_New.InGame;' }; $content | Set-Content '%FORKED_FILE%'"

echo Building final version...
cd /d "%USERPROFILE%\Documents\BTD6 Mod Sources\LightningMonkey"
dotnet build -c Release --nologo

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCCESS! Lightning Monkey mod is ready!
    echo ========================================
    echo.
    echo The mod has been built and copied to your Mods folder.
    echo Launch BTD6 to see your new Lightning Monkey in the Magic category!
) else (
    echo Build failed. Let me try a different approach...
    echo.
    echo Creating simplified tower without Game references...
    
    echo Creating corrected tower file...
    (
    echo using BTD_Mod_Helper.Api.Towers;
    echo using BTD_Mod_Helper.Extensions;
    echo using Il2CppAssets.Scripts.Models.Towers;
    echo using Il2CppAssets.Scripts.Models.TowerSets;
    echo.
    echo namespace LightningMonkey.Towers {
    echo   public class LightningMonkeyTower : ModTower {
    echo     public override string BaseTower =^> TowerType.DartMonkey;
    echo     public override TowerSet TowerSet =^> TowerSet.Magic;
    echo.
    echo     public override string DisplayName =^> "Lightning Monkey";
    echo     public override string Description =^> "Channels crackling bolts that chain through bloons.";
    echo     public override int Cost =^> 550;
    echo.
    echo     public override int TopPathUpgrades =^> 5;
    echo     public override int MiddlePathUpgrades =^> 5;
    echo     public override int BottomPathUpgrades =^> 5;
    echo.
    echo     public override void ModifyBaseTowerModel^(TowerModel m^) {
    echo       m.range += 8f;
    echo       var atk = m.GetAttackModel^(^);
    echo       atk.weapons[0].Rate *= 0.95f;
    echo       var p = atk.weapons[0].projectile;
    echo       p.pierce += 1;
    echo       p.GetDamageModel^(^).damage += 1;
    echo     }
    echo   }
    echo }
    ^) ^> "%TOWER_FILE%"
    
    echo Simplifying forked lightning upgrade...
    powershell -NoProfile -Command "(Get-Content '%FORKED_FILE%' -Raw) -replace 'var refW=Game\.instance\.model\.GetTowerFromId.*?;.*?w\.emission = refW\.emission\.Duplicate\(\);', 'w.projectile.pierce += 1;' | Set-Content '%FORKED_FILE%'"
    
    echo Building simplified version...
    dotnet build -c Release --nologo
    
    if %ERRORLEVEL% EQU 0 (
        echo ========================================
        echo SUCCESS! Lightning Monkey mod is ready!
        echo ========================================
        echo.
        echo The mod has been built and copied to your Mods folder.
        echo Launch BTD6 to see your new Lightning Monkey in the Magic category!
    ^) else (
        echo Build still failed. Check errors above.
    ^)
)
pause
